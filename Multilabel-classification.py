"""
FIJI/ImageJ Cross-Platform Production-Ready Batch Particle Analysis and Classification Script

This script performs automated batch analysis of particle images using FIJI/ImageJ:
1. Processes multiple images from a specified directory
2. Runs a macro to extract particle measurements
3. Uses a pre-trained Weka machine learning model to classify particles
4. Colors particles based on classification results
5. Exports statistics to CSV format

CROSS-PLATFORM COMPATIBILITY:
- Windows, macOS, and Linux support with automatic platform detection
- Environment variable configuration for easy deployment
- Platform-specific default path detection (FIJI installations, user directories)
- File permission validation for Unix-based systems
- Automatic path separator handling using os.path.join()
- Configuration template generation for deployment

PRODUCTION-READY FEATURES:
- Interactive folder selection: Automatic GUI dialog when no input directory is configured
- Resume capability: Automatically saves progress and can resume interrupted batches
- Comprehensive configuration validation: Prevents common user errors before processing
- Time estimation: Provides processing time estimates for planning
- Enhanced error handling: Graceful handling of failures with batch-level abort protection
- Checkpoint system: Saves progress every N images for recovery
- File permission checks: Validates read/write access across platforms
- Headless mode detection: Automatically adapts behavior for GUI vs. command-line environments

PERFORMANCE OPTIMIZATIONS:
- Model loaded once at startup (not per image) for 10x+ performance improvement
- Model analysis performed once at startup
- Modular function design for better maintainability
- Proper resource management with try/finally blocks
- Background processing mode for unattended operation
- Parallel processing foundation (future enhancement)

UNICODE SUPPORT:
This script uses direct Unicode handling approach - no character conversion needed.
Files with names like "particle_10µm.tif" or "sample_25°C.tif" are processed directly
without any Unicode-to-ASCII conversion, preserving original filenames throughout
the entire processing pipeline.

JYTHON COMPATIBILITY:
- All string formatting uses .format() method (no f-strings)
- Compatible with Jython 2.7 runtime used by ImageJ/FIJI
- Thread-safe operations prepared for future parallel processing
- Cross-platform file handling using standard Python libraries

ENVIRONMENT VARIABLES FOR DEPLOYMENT:
- PARTICLE_ANALYSIS_MACRO: Path to ImageJ macro file
- PARTICLE_ANALYSIS_MODEL: Path to Weka model file
- WEKA_JAR_PATH: Path to Weka JAR (if not in ImageJ classpath)
- PARTICLE_ANALYSIS_INPUT_DIR: Input directory containing images
- PARTICLE_ANALYSIS_FILE_TYPES: File extensions to process
- PARTICLE_ANALYSIS_FILTERS: Filename filters
- PARTICLE_ANALYSIS_CLEANUP: Enable/disable cleanup on exit

CONFIGURATION:
- Automatic platform detection and path configuration
- Environment variable override capability
- Comprehensive validation of all paths, files, and settings
- File permission validation for cross-platform compatibility
- Configuration template generation for easy deployment

Author: Cross-platform production-ready with environment variable configuration
Requirements: FIJI/ImageJ with Weka plugin (Windows/macOS/Linux)
Version: Cross-Platform v3.0 with environment variable support
"""

# === IMPORTS ===
from ij import IJ, WindowManager
from ij.measure import ResultsTable
from ij.plugin.frame import RoiManager
from java.io import FileInputStream, File, FileWriter, BufferedWriter
from java.awt import Color
from java.net import URL
from java.lang import ClassLoader, Class
from java.util import ArrayList
from javax.swing import JFileChooser, JOptionPane
from javax.swing.filechooser import FileSystemView
import sys
import os
import time

# === UNICODE HANDLING SETUP ===
# Note: Using direct Unicode handling approach - no encoding configuration needed
# Following the working implementation principle: "Don't convert Unicode - just use it directly"

# === CROSS-PLATFORM CONFIGURATION SECTION ===
# This section provides flexible configuration that works across Windows, macOS, and Linux

def get_config_value(env_var, default_value, description):
    """
    Get configuration value from environment variable or use default.

    Args:
        env_var: Environment variable name
        default_value: Default value if environment variable is not set
        description: Description for logging

    Returns:
        str: Configuration value
    """
    value = os.environ.get(env_var, default_value)
    if value == default_value and default_value:
        IJ.log("Using default {}: {}".format(description, value))
    elif value:
        IJ.log("Using {} from environment: {}".format(description, value))
    else:
        IJ.log("No {} specified".format(description))
    return value

def detect_platform_paths():
    """
    Detect platform-specific default paths for ImageJ/FIJI and common directories.

    Returns:
        dict: Dictionary containing platform-specific default paths
    """
    import platform
    system = platform.system().lower()
    home_dir = os.path.expanduser("~")

    defaults = {
        'system': system,
        'home_dir': home_dir,
        'fiji_dir': None,
        'documents_dir': None,
        'desktop_dir': None
    }

    if system == 'windows':
        # Windows default paths
        defaults['fiji_dir'] = os.path.join("C:", "Fiji.app")
        defaults['documents_dir'] = os.path.join(home_dir, "Documents")
        defaults['desktop_dir'] = os.path.join(home_dir, "Desktop")

    elif system == 'darwin':  # macOS
        # macOS default paths
        defaults['fiji_dir'] = "/Applications/Fiji.app"
        defaults['documents_dir'] = os.path.join(home_dir, "Documents")
        defaults['desktop_dir'] = os.path.join(home_dir, "Desktop")

    elif system == 'linux':
        # Linux default paths
        defaults['fiji_dir'] = os.path.join(home_dir, "Fiji.app")  # Common user installation
        defaults['documents_dir'] = os.path.join(home_dir, "Documents")
        defaults['desktop_dir'] = os.path.join(home_dir, "Desktop")

        # Alternative Linux paths to check
        alt_fiji_paths = [
            "/opt/Fiji.app",  # System-wide installation
            "/usr/local/Fiji.app",  # Local system installation
            os.path.join(home_dir, "ImageJ", "Fiji.app"),  # Alternative user location
        ]

        # Check if any alternative paths exist
        for alt_path in alt_fiji_paths:
            if os.path.exists(alt_path):
                defaults['fiji_dir'] = alt_path
                break

    return defaults

# Detect platform and get default paths
PLATFORM_DEFAULTS = detect_platform_paths()
IJ.log("Detected platform: {}".format(PLATFORM_DEFAULTS['system']))

# === CONFIGURABLE PATHS ===
# These can be overridden by environment variables for easy deployment

# Macro file path
# Environment variable: PARTICLE_ANALYSIS_MACRO
# Default: Look in FIJI macros directory or current directory
default_macro_name = "extract_ASC_for_batch_classification.ijm"
default_macro_path = ""
if PLATFORM_DEFAULTS['fiji_dir'] and os.path.exists(PLATFORM_DEFAULTS['fiji_dir']):
    default_macro_path = os.path.join(PLATFORM_DEFAULTS['fiji_dir'], "macros", default_macro_name)
if not default_macro_path or not os.path.exists(default_macro_path):
    default_macro_path = os.path.join(os.getcwd(), "macros", default_macro_name)

MACRO_PATH = get_config_value(
    "PARTICLE_ANALYSIS_MACRO",
    default_macro_path,
    "macro file path"
)

# Model file path
# Environment variable: PARTICLE_ANALYSIS_MODEL
# Default: Look in current directory or Documents
default_model_name = "perceptron_3_classes_ASC.model"
default_model_path = os.path.join(os.getcwd(), "models",default_model_name)
if not os.path.exists(default_model_path) and PLATFORM_DEFAULTS['documents_dir']:
    default_model_path = os.path.join(PLATFORM_DEFAULTS['documents_dir'], "ParticleAnalysis", default_model_name)

MODEL_PATH = get_config_value(
    "PARTICLE_ANALYSIS_MODEL",
    default_model_path,
    "model file path"
)

# Weka JAR path (optional)
# Environment variable: WEKA_JAR_PATH
# Default: Platform-specific common locations
default_weka_paths = []
if PLATFORM_DEFAULTS['system'] == 'windows':
    default_weka_paths = [
        "C:/Program Files/Weka-3-8-6/weka.jar",
        "C:/Program Files (x86)/Weka-3-8-6/weka.jar"
    ]
elif PLATFORM_DEFAULTS['system'] == 'darwin':
    default_weka_paths = [
        "/Applications/weka-3-8-6/weka.jar",
        os.path.join(PLATFORM_DEFAULTS['home_dir'], "Applications", "weka-3-8-6", "weka.jar")
    ]
elif PLATFORM_DEFAULTS['system'] == 'linux':
    default_weka_paths = [
        "/usr/share/java/weka.jar",
        "/opt/weka/weka.jar",
        os.path.join(PLATFORM_DEFAULTS['home_dir'], "weka", "weka.jar")
    ]

default_weka_jar = ""
for weka_path in default_weka_paths:
    if os.path.exists(weka_path):
        default_weka_jar = weka_path
        break

USER_WEKA_JAR_PATH = get_config_value(
    "WEKA_JAR_PATH",
    default_weka_jar,
    "Weka JAR path"
)

# Input directory
# Environment variable: PARTICLE_ANALYSIS_INPUT_DIR
default_input_dir = ""
if PLATFORM_DEFAULTS['documents_dir']:
    alt_input_dir = os.path.join(PLATFORM_DEFAULTS['documents_dir'], "ParticleAnalysis", "Input")
    if os.path.exists(alt_input_dir):
        default_input_dir = alt_input_dir

IMPORT_DIR = get_config_value(
    "PARTICLE_ANALYSIS_INPUT_DIR",
    default_input_dir,
    "input directory"
)

# Batch processing settings
FILE_TYPES = get_config_value("PARTICLE_ANALYSIS_FILE_TYPES", "tif", "file types filter")
FILTERS = get_config_value("PARTICLE_ANALYSIS_FILTERS", "", "filename filters")
CLEANUP_ON_EXIT = get_config_value("PARTICLE_ANALYSIS_CLEANUP", "True", "cleanup on exit").lower() == "true"

def check_file_permissions():
    """
    Check file system permissions for cross-platform compatibility.

    Returns:
        dict: Dictionary containing permission check results
    """
    permissions = {
        'macro_readable': False,
        'model_readable': False,
        'input_readable': False,
        'output_writable': False,
        'temp_writable': False
    }

    # Check macro file permissions
    if os.path.exists(MACRO_PATH):
        try:
            with open(MACRO_PATH, 'r') as f:
                f.read(1)  # Try to read one character
            permissions['macro_readable'] = True
        except Exception as e:
            IJ.log("WARNING: Cannot read macro file: " + str(e))

    # Check model file permissions
    if os.path.exists(MODEL_PATH):
        try:
            with open(MODEL_PATH, 'rb') as f:
                f.read(1)  # Try to read one byte
            permissions['model_readable'] = True
        except Exception as e:
            IJ.log("WARNING: Cannot read model file: " + str(e))

    # Check input directory permissions
    if os.path.exists(IMPORT_DIR):
        try:
            os.listdir(IMPORT_DIR)
            permissions['input_readable'] = True
        except Exception as e:
            IJ.log("WARNING: Cannot read input directory: " + str(e))

    # Check output directory permissions
    output_dir = os.path.dirname(IMPORT_DIR) if IMPORT_DIR else os.getcwd()
    try:
        test_file = os.path.join(output_dir, "test_write_permissions.tmp")
        with open(test_file, 'w') as f:
            f.write("test")
        os.remove(test_file)
        permissions['output_writable'] = True
    except Exception as e:
        IJ.log("WARNING: Cannot write to output directory {}: {}".format(output_dir, str(e)))

    # Check temp directory permissions
    import tempfile
    try:
        with tempfile.NamedTemporaryFile(delete=True) as tmp:
            tmp.write(b"test")
        permissions['temp_writable'] = True
    except Exception as e:
        IJ.log("WARNING: Cannot write to temp directory: " + str(e))

    return permissions

def create_config_template():
    """
    Create a configuration template file for easy deployment.

    Returns:
        str: Path to created configuration template
    """
    template_content = """# Cross-Platform Particle Analysis Configuration Template
# Copy this file and set environment variables or modify paths as needed

# === ENVIRONMENT VARIABLES FOR CROSS-PLATFORM DEPLOYMENT ===
# Set these environment variables to override default paths:

# Macro file path
# export PARTICLE_ANALYSIS_MACRO="/path/to/your/macro.ijm"

# Model file path
# export PARTICLE_ANALYSIS_MODEL="/path/to/your/model.model"

# Weka JAR path (optional, if not in ImageJ classpath)
# export WEKA_JAR_PATH="/path/to/weka.jar"

# Input directory containing images to process
# export PARTICLE_ANALYSIS_INPUT_DIR="/path/to/input/images"

# File types to process (semicolon-separated)
# export PARTICLE_ANALYSIS_FILE_TYPES="tif;jpg;png"

# Filename filters (semicolon-separated, optional)
# export PARTICLE_ANALYSIS_FILTERS="sample;test"

# Cleanup on exit (true/false)
# export PARTICLE_ANALYSIS_CLEANUP="true"

# === PLATFORM-SPECIFIC EXAMPLES ===

# Windows Example:
# set PARTICLE_ANALYSIS_MACRO=C:\\Users\\<USER>\\Fiji.app\\macros\\analysis.ijm
# set PARTICLE_ANALYSIS_MODEL=C:\\Users\\<USER>\\Documents\\model.model
# set PARTICLE_ANALYSIS_INPUT_DIR=C:\\Users\\<USER>\\Documents\\Images

# macOS Example:
# export PARTICLE_ANALYSIS_MACRO="/Applications/Fiji.app/macros/analysis.ijm"
# export PARTICLE_ANALYSIS_MODEL="/Users/<USER>/Documents/model.model"
# export PARTICLE_ANALYSIS_INPUT_DIR="/Users/<USER>/Documents/Images"

# Linux Example:
# export PARTICLE_ANALYSIS_MACRO="/home/<USER>/Fiji.app/macros/analysis.ijm"
# export PARTICLE_ANALYSIS_MODEL="/home/<USER>/Documents/model.model"
# export PARTICLE_ANALYSIS_INPUT_DIR="/home/<USER>/Documents/Images"

# === DEPLOYMENT SCRIPT EXAMPLES ===

# Linux/macOS deployment script (deploy.sh):
# #!/bin/bash
# export PARTICLE_ANALYSIS_MACRO="$HOME/Fiji.app/macros/analysis.ijm"
# export PARTICLE_ANALYSIS_MODEL="$HOME/Documents/ParticleAnalysis/model.model"
# export PARTICLE_ANALYSIS_INPUT_DIR="$HOME/Documents/ParticleAnalysis/Input"
# /path/to/fiji --headless --run /path/to/Batch-processing_Augment.py

# Windows deployment script (deploy.bat):
# @echo off
# set PARTICLE_ANALYSIS_MACRO=%USERPROFILE%\\Fiji.app\\macros\\analysis.ijm
# set PARTICLE_ANALYSIS_MODEL=%USERPROFILE%\\Documents\\ParticleAnalysis\\model.model
# set PARTICLE_ANALYSIS_INPUT_DIR=%USERPROFILE%\\Documents\\ParticleAnalysis\\Input
# "C:\\Fiji.app\\ImageJ-win64.exe" --headless --run "C:\\path\\to\\Batch-processing_Augment.py"
"""

    try:
        template_path = os.path.join(os.getcwd(), "particle_analysis_config_template.txt")
        with open(template_path, 'w') as f:
            f.write(template_content)
        IJ.log("Configuration template created: " + template_path)
        return template_path
    except Exception as e:
        IJ.log("WARNING: Could not create configuration template: " + str(e))
        return None

def is_headless_mode():
    """
    Check if ImageJ is running in headless mode (no GUI available).

    Returns:
        bool: True if headless, False if GUI is available
    """
    try:
        # Check if we're in headless mode
        import java.awt.GraphicsEnvironment as GraphicsEnvironment
        return GraphicsEnvironment.isHeadless()
    except:
        # If we can't determine, assume headless for safety
        return True

def show_folder_selection_dialog():
    """
    Show an interactive folder selection dialog for choosing the input directory.

    Returns:
        str: Selected directory path, or None if cancelled
    """
    try:
        # Check if we're in headless mode
        if is_headless_mode():
            IJ.log("Running in headless mode - cannot show folder selection dialog")
            return None

        IJ.log("-> Opening folder selection dialog...")

        # Method 1: Try using ImageJ's built-in directory chooser
        try:
            selected_dir = IJ.getDirectory("Select Directory Containing Images to Analyze")
            if selected_dir:
                # IJ.getDirectory() returns path with trailing separator, normalize it
                selected_dir = os.path.normpath(selected_dir)
                IJ.log("-> Directory selected via ImageJ dialog: " + selected_dir)
                return selected_dir
            else:
                IJ.log("-> User cancelled folder selection")
                return None
        except Exception as e:
            IJ.log("-> ImageJ dialog failed, trying Java Swing dialog: " + str(e))

        # Method 2: Fallback to Java Swing JFileChooser
        try:
            # Create file chooser
            chooser = JFileChooser()
            chooser.setDialogTitle("Select Directory Containing Images to Analyze")
            chooser.setFileSelectionMode(JFileChooser.DIRECTORIES_ONLY)
            chooser.setAcceptAllFileFilterUsed(False)

            # Set default starting directory
            default_start = PLATFORM_DEFAULTS.get('documents_dir', PLATFORM_DEFAULTS.get('home_dir', os.getcwd()))
            if default_start and os.path.exists(default_start):
                chooser.setCurrentDirectory(File(default_start))

            # Show dialog
            result = chooser.showOpenDialog(None)

            if result == JFileChooser.APPROVE_OPTION:
                selected_file = chooser.getSelectedFile()
                selected_dir = selected_file.getAbsolutePath()
                IJ.log("-> Directory selected via Swing dialog: " + selected_dir)
                return selected_dir
            else:
                IJ.log("-> User cancelled folder selection")
                return None

        except Exception as e:
            IJ.log("-> Swing dialog also failed: " + str(e))
            return None

    except Exception as e:
        IJ.log("ERROR: Failed to show folder selection dialog: " + str(e))
        return None

def validate_selected_directory(directory_path):
    """
    Validate a user-selected directory for suitability as input directory.

    Args:
        directory_path: Path to validate

    Returns:
        tuple: (is_valid, error_message, warning_message)
    """
    if not directory_path:
        return False, "No directory selected", None

    if not os.path.exists(directory_path):
        return False, "Selected directory does not exist: " + directory_path, None

    if not os.path.isdir(directory_path):
        return False, "Selected path is not a directory: " + directory_path, None

    # Check read permissions
    try:
        os.listdir(directory_path)
    except Exception as e:
        return False, "Cannot read selected directory: " + str(e), None

    # Check for image files
    try:
        file_types = FILE_TYPES.split(';') if FILE_TYPES else ['tif', 'jpg', 'png', 'bmp']
        image_files = []

        for filename in os.listdir(directory_path):
            file_path = os.path.join(directory_path, filename)
            if os.path.isfile(file_path):
                for file_type in file_types:
                    if filename.lower().endswith('.' + file_type.lower().strip()):
                        image_files.append(filename)
                        break

        if len(image_files) == 0:
            warning_msg = "No image files found in selected directory (looking for: {})".format(', '.join(file_types))
            return True, None, warning_msg
        else:
            return True, None, None

    except Exception as e:
        return False, "Error scanning directory for image files: " + str(e), None

def handle_interactive_folder_selection():
    """
    Handle interactive folder selection when no valid input directory is configured.

    Returns:
        str: Valid directory path, or None if selection failed/cancelled
    """
    global IMPORT_DIR

    IJ.log("\n" + "=" * 60)
    IJ.log("[INTERACTIVE FOLDER SELECTION]")
    IJ.log("=" * 60)
    IJ.log("No valid input directory configured.")
    IJ.log("Current IMPORT_DIR: " + str(IMPORT_DIR))

    # Check if we can show GUI dialogs
    if is_headless_mode():
        IJ.log("ERROR: Running in headless mode - cannot show interactive folder selection dialog")
        IJ.log("Please set the PARTICLE_ANALYSIS_INPUT_DIR environment variable or")
        IJ.log("modify the IMPORT_DIR configuration in the script.")
        return None

    # Show folder selection dialog
    selected_dir = show_folder_selection_dialog()

    if not selected_dir:
        IJ.log("ERROR: No directory selected. Cannot proceed without input directory.")
        return None

    # Validate selected directory
    is_valid, error_msg, warning_msg = validate_selected_directory(selected_dir)

    if not is_valid:
        IJ.log("ERROR: Selected directory is not valid: " + error_msg)

        # Offer to try again
        if not is_headless_mode():
            try:
                retry = JOptionPane.showConfirmDialog(
                    None,
                    "Selected directory is not valid:\n" + error_msg + "\n\nWould you like to select a different directory?",
                    "Invalid Directory",
                    JOptionPane.YES_NO_OPTION,
                    JOptionPane.ERROR_MESSAGE
                )

                if retry == JOptionPane.YES_OPTION:
                    IJ.log("-> User chose to select a different directory")
                    return handle_interactive_folder_selection()  # Recursive retry
                else:
                    IJ.log("-> User chose not to retry")
                    return None
            except:
                # If dialog fails, just return None
                return None
        else:
            return None

    # Directory is valid, update configuration
    IMPORT_DIR = selected_dir
    IJ.log("SUCCESS: Input directory updated to: " + IMPORT_DIR)

    if warning_msg:
        IJ.log("WARNING: " + warning_msg)

        # Show warning dialog if GUI is available
        if not is_headless_mode():
            try:
                proceed = JOptionPane.showConfirmDialog(
                    None,
                    warning_msg + "\n\nDo you want to proceed anyway?",
                    "Directory Warning",
                    JOptionPane.YES_NO_OPTION,
                    JOptionPane.WARNING_MESSAGE
                )

                if proceed != JOptionPane.YES_OPTION:
                    IJ.log("-> User chose not to proceed with warning")
                    return handle_interactive_folder_selection()  # Try again
            except:
                # If dialog fails, just proceed
                pass

    return IMPORT_DIR

# Feature configuration - CRITICAL: Must match your Weka model's training features
# Update these to match the exact feature names and order your model expects
REQUIRED_FEATURES = ["%Area", "Solidity", "Convexity"]  # Example: replace with your actual features

# Classification labels - should match your Weka model's class labels
DEFAULT_CLASS_LABELS = ["perfect", "porous", "satellites"]

# Color scheme for particle visualization (RGB with alpha transparency)
# Colors correspond to class labels in order: perfect=red, porous=green, satellites=blue
PARTICLE_COLORS = [
    Color(255, 0, 0, 100),    # Red with transparency for class 1
    Color(0, 255, 0, 100),    # Green with transparency for class 2
    Color(0, 0, 255, 100)     # Blue with transparency for class 3
]

# Performance settings
BATCH_SIZE = 100              # Number of particles to process at once (memory optimization)
LOG_PROGRESS_INTERVAL = 10    # Log progress every N images

# Image display settings
SHOW_IMAGES_DURING_PROCESSING = True  # Display images during processing
# True  = Visual monitoring mode: Images displayed for real-time monitoring (slower, uses screen space)
# False = Background mode: Images registered for macro execution but windows hidden (faster, unattended processing)
# Note: ImageJ macros require images to be registered with WindowManager. In background mode,
#       images are briefly shown then hidden to ensure macro compatibility while minimizing visual overhead.

# Resume capability settings
ENABLE_RESUME = True          # Enable checkpoint/resume functionality
CHECKPOINT_INTERVAL = 5       # Save progress every N images
CHECKPOINT_FILE = "batch_processing_checkpoint.json"  # Checkpoint file name
SKIP_PROCESSED_IMAGES = True  # Skip images that were already processed successfully

# Parallel processing settings (future enhancement)
ENABLE_PARALLEL_PROCESSING = False  # Enable parallel image processing (experimental)
MAX_WORKER_THREADS = 2              # Maximum number of worker threads for parallel processing
THREAD_SAFE_MODE = True             # Use thread-safe operations when parallel processing is enabled

# === GLOBAL VARIABLES ===
# Weka classes are imported dynamically and stored globally for performance
WekaAttribute = None
WekaInstances = None
WekaDenseInstance = None
WekaSerializationHelper = None

# Global classifier loaded once at startup for performance optimization
GLOBAL_CLASSIFIER = None
GLOBAL_MODEL_FEATURES = None
GLOBAL_MODEL_CLASSES = None

# === HELPER FUNCTIONS ===
# Note: Using direct Unicode handling approach - no conversion functions needed

def save_checkpoint(checkpoint_data, checkpoint_path):
    """
    Save processing checkpoint to enable resume functionality.

    Args:
        checkpoint_data: Dictionary containing processing state
        checkpoint_path: Path to save checkpoint file
    """
    try:
        import json
        with open(checkpoint_path, 'w') as f:
            json.dump(checkpoint_data, f, indent=2)
        IJ.log("-> Checkpoint saved: {} images processed".format(checkpoint_data.get('processed_count', 0)))
        return True
    except Exception as e:
        IJ.log("WARNING: Failed to save checkpoint: " + str(e))
        return False

def load_checkpoint(checkpoint_path):
    """
    Load processing checkpoint to resume interrupted batch.

    Args:
        checkpoint_path: Path to checkpoint file

    Returns:
        dict: Checkpoint data or None if not found/invalid
    """
    try:
        import json
        if not os.path.exists(checkpoint_path):
            return None

        with open(checkpoint_path, 'r') as f:
            checkpoint_data = json.load(f)

        IJ.log("-> Checkpoint found: {} images previously processed".format(
            checkpoint_data.get('processed_count', 0)))
        return checkpoint_data

    except Exception as e:
        IJ.log("WARNING: Failed to load checkpoint: " + str(e))
        return None

def clear_checkpoint(checkpoint_path):
    """Remove checkpoint file after successful completion."""
    try:
        if os.path.exists(checkpoint_path):
            os.remove(checkpoint_path)
            IJ.log("-> Checkpoint file cleared")
    except Exception as e:
        IJ.log("WARNING: Failed to clear checkpoint: " + str(e))

def prepare_parallel_processing():
    """
    Prepare for parallel processing (future enhancement).

    Currently returns False as parallel processing is not yet implemented
    due to Jython threading limitations and ImageJ thread safety concerns.

    Returns:
        bool: True if parallel processing is ready, False otherwise
    """
    if not ENABLE_PARALLEL_PROCESSING:
        return False

    # Future implementation would check:
    # - Thread safety of ImageJ operations
    # - Weka classifier thread safety
    # - Memory requirements for multiple threads
    # - Jython threading capabilities

    IJ.log("WARNING: Parallel processing is not yet implemented")
    IJ.log("-> Using single-threaded processing for maximum stability")
    return False

def estimate_processing_time(num_images, avg_particles_per_image=50):
    """
    Estimate total processing time based on image count and complexity.

    Args:
        num_images: Number of images to process
        avg_particles_per_image: Estimated average particles per image

    Returns:
        dict: Time estimates in different units
    """
    # Base time estimates (in seconds)
    time_per_image_base = 2.0  # Base time for image loading and macro execution
    time_per_particle = 0.01   # Time per particle for feature extraction and classification

    estimated_seconds = num_images * (time_per_image_base + avg_particles_per_image * time_per_particle)

    return {
        'seconds': estimated_seconds,
        'minutes': estimated_seconds / 60.0,
        'hours': estimated_seconds / 3600.0,
        'formatted': "{:.1f} minutes".format(estimated_seconds / 60.0) if estimated_seconds < 3600
                    else "{:.1f} hours".format(estimated_seconds / 3600.0)
    }

def format_elapsed_time(elapsed_seconds):
    """
    Format elapsed time in a human-readable format.

    Args:
        elapsed_seconds: Time elapsed in seconds

    Returns:
        str: Formatted time string (e.g., "2h 15m 30s", "45m 12s", "23s")
    """
    if elapsed_seconds < 60:
        return "{:.1f}s".format(elapsed_seconds)
    elif elapsed_seconds < 3600:
        minutes = int(elapsed_seconds // 60)
        seconds = elapsed_seconds % 60
        return "{}m {:.1f}s".format(minutes, seconds)
    else:
        hours = int(elapsed_seconds // 3600)
        minutes = int((elapsed_seconds % 3600) // 60)
        seconds = elapsed_seconds % 60
        if seconds < 1:
            return "{}h {}m".format(hours, minutes)
        else:
            return "{}h {}m {:.0f}s".format(hours, minutes, seconds)

def log_performance_summary(start_time, end_time, processed_count, total_particles):
    """
    Log a comprehensive performance summary of the batch processing.

    Args:
        start_time: Processing start time (from time.time())
        end_time: Processing end time (from time.time())
        processed_count: Number of images successfully processed
        total_particles: Total number of particles classified
    """
    elapsed_seconds = end_time - start_time
    elapsed_formatted = format_elapsed_time(elapsed_seconds)

    IJ.log("\n" + "=" * 60)
    IJ.log("[PERFORMANCE SUMMARY]")
    IJ.log("=" * 60)

    # Basic timing information
    IJ.log("Total processing time: {}".format(elapsed_formatted))
    IJ.log("Images processed: {}".format(processed_count))
    IJ.log("Particles classified: {}".format(total_particles))

    # Performance metrics
    if processed_count > 0:
        avg_time_per_image = elapsed_seconds / processed_count
        IJ.log("Average time per image: {:.1f}s".format(avg_time_per_image))

        if total_particles > 0:
            avg_time_per_particle = elapsed_seconds / total_particles
            avg_particles_per_image = total_particles / float(processed_count)
            IJ.log("Average particles per image: {:.1f}".format(avg_particles_per_image))
            IJ.log("Average time per particle: {:.3f}s".format(avg_time_per_particle))

            # Processing rate
            images_per_minute = processed_count / (elapsed_seconds / 60.0) if elapsed_seconds > 0 else 0
            particles_per_minute = total_particles / (elapsed_seconds / 60.0) if elapsed_seconds > 0 else 0
            IJ.log("Processing rate: {:.1f} images/min, {:.0f} particles/min".format(
                images_per_minute, particles_per_minute))

    # Display mode performance note
    if SHOW_IMAGES_DURING_PROCESSING:
        IJ.log("Processing mode: Visual monitoring (with image display)")
    else:
        IJ.log("Processing mode: Background processing (optimized performance)")

    IJ.log("=" * 60)

def ensure_weka_in_classpath():
    """
    Ensures Weka machine learning library is available in the ImageJ classpath.

    This function first checks if Weka classes can be imported directly.
    If not, it attempts to dynamically add the Weka JAR file to the classpath.

    Returns:
        bool: True if Weka is successfully available, False otherwise
    """
    try:
        # Test if Weka is already available by trying to load a core class
        Class.forName("weka.core.Attribute")
        IJ.log("SUCCESS: Weka libraries found in ImageJ classpath")
        return True

    except Exception as e:
        IJ.log("WARNING: Weka libraries not found in classpath: " + str(e))

        # Validate Weka JAR path configuration
        if not USER_WEKA_JAR_PATH:
            IJ.log("ERROR: No Weka JAR path configured. Please set USER_WEKA_JAR_PATH.")
            return False

        if not os.path.exists(USER_WEKA_JAR_PATH):
            IJ.log("ERROR: Weka JAR not found at: " + USER_WEKA_JAR_PATH)
            return False

        # Attempt to dynamically add Weka JAR to classpath
        IJ.log("-> Attempting to add Weka to classpath from: " + USER_WEKA_JAR_PATH)
        try:
            # Use Java reflection to add JAR to system classloader
            jar_file = File(USER_WEKA_JAR_PATH)
            jar_url = jar_file.toURI().toURL()
            sys_loader = ClassLoader.getSystemClassLoader()
            method = sys_loader.getClass().getDeclaredMethod("addURL", [URL])
            method.setAccessible(True)
            method.invoke(sys_loader, [jar_url])
            IJ.log("-> Weka JAR added to classpath")

            # Verify Weka is now accessible
            Class.forName("weka.core.Attribute")
            IJ.log("SUCCESS: Weka classes now accessible")
            return True

        except Exception as e2:
            IJ.log("ERROR: Failed to load Weka: " + str(e2))
            IJ.log("  Try restarting ImageJ for classpath changes to take effect")
            return False

def import_weka_classes():
    """
    Import Weka classes and store them in global variables.
    This must be called after ensuring Weka is in classpath.
    """
    global WekaAttribute, WekaInstances, WekaDenseInstance, WekaSerializationHelper

    try:
        # Import core Weka classes
        from weka.core import Attribute as WekaAttribute
        from weka.core import Instances as WekaInstances
        from weka.core import DenseInstance as WekaDenseInstance
        from weka.core import SerializationHelper as WekaSerializationHelper

        IJ.log("Successfully imported Weka classes:")
        IJ.log("  - Attribute: " + str(WekaAttribute))
        IJ.log("  - Instances: " + str(WekaInstances))
        IJ.log("  - DenseInstance: " + str(WekaDenseInstance))
        IJ.log("  - SerializationHelper: " + str(WekaSerializationHelper))
        return True

    except ImportError as e:
        IJ.log("Failed to import Weka classes: " + str(e))
        IJ.log("Available Weka classes might be limited. Check your Weka installation.")
        return False
    except Exception as e:
        IJ.log("Unexpected error importing Weka classes: " + str(e))
        return False

def test_weka_functionality():
    """Test basic Weka functionality to ensure everything works"""
    try:
        # Test creating a simple attribute
        test_attr = WekaAttribute("test_numeric")
        IJ.log("Test: Created numeric attribute successfully")

        # Test creating nominal attribute
        nominal_values = ArrayList()
        nominal_values.add("class1")
        nominal_values.add("class2")
        test_nominal = WekaAttribute("test_nominal", nominal_values)
        IJ.log("Test: Created nominal attribute successfully")

        # Test creating instances structure
        attrs = ArrayList()
        attrs.add(test_attr)
        attrs.add(test_nominal)
        test_instances = WekaInstances("test", attrs, 0)
        test_instances.setClassIndex(1)
        IJ.log("Test: Created Instances structure successfully")

        return True

    except Exception as e:
        IJ.log("Weka functionality test failed: " + str(e))
        return False

def validate_configuration():
    """
    Comprehensive validation of all configuration settings before running the main script.

    Checks for existence of required files, validates settings, and provides helpful
    error messages if any configuration issues are found.

    Returns:
        bool: True if configuration is valid, False if critical errors found
    """
    errors = []
    warnings = []
    info = []

    IJ.log("-> Performing comprehensive configuration validation...")

    # === FILE EXISTENCE CHECKS ===
    # Check macro file exists and is readable
    if not os.path.exists(MACRO_PATH):
        errors.append("Macro file not found: " + MACRO_PATH)
    elif not os.path.isfile(MACRO_PATH):
        errors.append("Macro path is not a file: " + MACRO_PATH)
    else:
        info.append("Macro file found: " + os.path.basename(MACRO_PATH))

    # Check model file exists and is readable
    if not os.path.exists(MODEL_PATH):
        errors.append("Model file not found: " + MODEL_PATH)
    elif not os.path.isfile(MODEL_PATH):
        errors.append("Model path is not a file: " + MODEL_PATH)
    else:
        # Check model file size
        model_size = os.path.getsize(MODEL_PATH)
        if model_size == 0:
            errors.append("Model file is empty: " + MODEL_PATH)
        else:
            info.append("Model file found: {} ({:.1f} KB)".format(
                os.path.basename(MODEL_PATH), model_size / 1024.0))

    # Check input directory exists and is accessible
    input_dir_valid = False
    if not IMPORT_DIR or not os.path.exists(IMPORT_DIR) or not os.path.isdir(IMPORT_DIR):
        # Input directory is invalid - trigger interactive selection if possible
        IJ.log("-> Input directory is invalid, attempting interactive selection...")

        # Try interactive folder selection
        selected_dir = handle_interactive_folder_selection()

        if selected_dir:
            # Re-validate with the newly selected directory
            if os.path.exists(selected_dir) and os.path.isdir(selected_dir):
                input_dir_valid = True
                info.append("Input directory selected interactively: " + selected_dir)

                # Check directory content
                try:
                    file_count = len([f for f in os.listdir(selected_dir) if os.path.isfile(os.path.join(selected_dir, f))])
                    if file_count == 0:
                        warnings.append("Selected directory is empty: " + selected_dir)
                    else:
                        info.append("Selected directory contains: {} files".format(file_count))
                except Exception as e:
                    warnings.append("Cannot fully access selected directory: " + str(e))
            else:
                errors.append("Selected directory is not valid: " + str(selected_dir))
        else:
            errors.append("No valid input directory configured and interactive selection failed/cancelled")
    else:
        # Input directory is valid from configuration
        input_dir_valid = True
        try:
            file_count = len([f for f in os.listdir(IMPORT_DIR) if os.path.isfile(os.path.join(IMPORT_DIR, f))])
            if file_count == 0:
                warnings.append("Input directory is empty: " + IMPORT_DIR)
            else:
                info.append("Input directory found: {} files".format(file_count))
        except Exception as e:
            errors.append("Cannot access input directory: " + str(e))
            input_dir_valid = False

    # Check Weka JAR if specified
    if USER_WEKA_JAR_PATH:
        if not os.path.exists(USER_WEKA_JAR_PATH):
            warnings.append("Weka JAR not found at: " + USER_WEKA_JAR_PATH)
        else:
            info.append("Weka JAR found: " + os.path.basename(USER_WEKA_JAR_PATH))

    # === CONFIGURATION VALIDATION ===
    # Check required features configuration
    if not REQUIRED_FEATURES:
        warnings.append("REQUIRED_FEATURES is empty. Auto-configuration from model will be attempted.")
    else:
        info.append("Required features configured: {} features".format(len(REQUIRED_FEATURES)))

    # Check class labels configuration
    if not DEFAULT_CLASS_LABELS:
        errors.append("DEFAULT_CLASS_LABELS is empty. At least one class label is required.")
    else:
        info.append("Class labels configured: {}".format(DEFAULT_CLASS_LABELS))

    # Check color configuration
    if len(PARTICLE_COLORS) < len(DEFAULT_CLASS_LABELS):
        warnings.append("Not enough colors defined for all classes. Some classes may use default colors.")

    # Check file type filters
    if not FILE_TYPES:
        warnings.append("FILE_TYPES is empty. All file types will be processed.")
    else:
        info.append("File type filters: {}".format(FILE_TYPES))

    # === PERFORMANCE SETTINGS VALIDATION ===
    if BATCH_SIZE <= 0:
        warnings.append("BATCH_SIZE should be positive. Using default value.")

    if LOG_PROGRESS_INTERVAL <= 0:
        warnings.append("LOG_PROGRESS_INTERVAL should be positive. Using default value.")

    if CHECKPOINT_INTERVAL <= 0:
        warnings.append("CHECKPOINT_INTERVAL should be positive. Using default value.")

    # Validate image display setting
    if SHOW_IMAGES_DURING_PROCESSING:
        info.append("Image display mode: Visual monitoring enabled")
    else:
        info.append("Image display mode: Background processing (faster performance)")

    # === OUTPUT DIRECTORY VALIDATION ===
    output_dir = os.path.dirname(IMPORT_DIR)
    if not os.path.exists(output_dir):
        errors.append("Output directory (parent of input) not found: " + output_dir)
    elif not os.access(output_dir, os.W_OK):
        errors.append("Output directory is not writable: " + output_dir)
    else:
        info.append("Output directory is writable: " + output_dir)

    # === CROSS-PLATFORM PERMISSION CHECKS ===
    IJ.log("-> Checking file system permissions...")
    permissions = check_file_permissions()

    if not permissions['macro_readable'] and os.path.exists(MACRO_PATH):
        errors.append("Macro file exists but is not readable: " + MACRO_PATH)
    elif permissions['macro_readable']:
        info.append("Macro file is readable")

    if not permissions['model_readable'] and os.path.exists(MODEL_PATH):
        errors.append("Model file exists but is not readable: " + MODEL_PATH)
    elif permissions['model_readable']:
        info.append("Model file is readable")

    if not permissions['input_readable'] and os.path.exists(IMPORT_DIR):
        errors.append("Input directory exists but is not readable: " + IMPORT_DIR)
    elif permissions['input_readable']:
        info.append("Input directory is readable")

    if not permissions['output_writable']:
        warnings.append("Output directory may not be writable - CSV export might fail")
    else:
        info.append("Output directory has write permissions")

    if not permissions['temp_writable']:
        warnings.append("Temporary directory is not writable - checkpoint functionality might fail")
    else:
        info.append("Temporary directory is writable")

    # === PLATFORM-SPECIFIC INFORMATION ===
    info.append("Platform: {} ({})".format(PLATFORM_DEFAULTS['system'],
                                          "64-bit" if "64" in str(os.uname() if hasattr(os, 'uname') else "unknown") else "unknown"))
    info.append("Home directory: " + PLATFORM_DEFAULTS['home_dir'])
    if PLATFORM_DEFAULTS['fiji_dir']:
        if os.path.exists(PLATFORM_DEFAULTS['fiji_dir']):
            info.append("FIJI installation detected: " + PLATFORM_DEFAULTS['fiji_dir'])
        else:
            warnings.append("FIJI installation path not found: " + PLATFORM_DEFAULTS['fiji_dir'])

    # === REPORT VALIDATION RESULTS ===
    if info:
        IJ.log("CONFIGURATION INFO:")
        for item in info:
            IJ.log("  + " + item)

    if warnings:
        IJ.log("WARNING: CONFIGURATION WARNINGS:")
        for warning in warnings:
            IJ.log("  ! " + warning)

    if errors:
        IJ.log("ERROR: CONFIGURATION ERRORS:")
        for error in errors:
            IJ.log("  - " + error)
        IJ.log("Please fix the above errors before running the script.")
        return False

    IJ.log("SUCCESS: Configuration validation passed ({} checks completed)".format(
        len(info) + len(warnings)))
    return True

def load_weka_model(model_path):
    """Load and validate Weka model with proper error handling and resource management"""
    if not os.path.exists(model_path):
        IJ.log("Error: Model file not found: " + model_path)
        return None

    IJ.log("Loading Weka model from: " + model_path)
    file_stream = None
    try:
        # Load the model using the imported SerializationHelper with proper resource management
        file_stream = FileInputStream(model_path)
        loaded_object = WekaSerializationHelper.read(file_stream)

        # Check if it's a classifier
        if hasattr(loaded_object, 'classifyInstance'):
            IJ.log("Weka model loaded successfully: " + loaded_object.getClass().getName())
            return loaded_object
        else:
            IJ.log("Error: Loaded object is not a Weka Classifier")
            IJ.log("Object type: " + str(type(loaded_object)))
            return None

    except Exception as e:
        IJ.log("Error loading Weka model: " + str(e))
        return None
    finally:
        # Ensure file stream is always closed
        if file_stream is not None:
            try:
                file_stream.close()
            except Exception as cleanup_error:
                IJ.log("Warning: Error closing model file stream: " + str(cleanup_error))

def analyze_weka_model(classifier):
    """
    Analyze the loaded Weka classifier to extract information about
    required features and class labels.

    Returns:
        tuple: (feature_names_list, class_labels_list, analysis_success)
    """
    IJ.log("Analyzing Weka model structure...")

    try:
        # Try to get capabilities - this tells us about input requirements
        if hasattr(classifier, 'getCapabilities'):
            capabilities = classifier.getCapabilities()
            IJ.log("Model capabilities obtained")
        else:
            IJ.log("Model doesn't expose capabilities")
            capabilities = None

        # Try to get training header/instances structure
        training_header = None
        feature_names = []
        class_labels = []

        # Method 1: Check if model stores training header
        if hasattr(classifier, 'getHeader'):
            try:
                training_header = classifier.getHeader()
                IJ.log("Found training header in model")
            except:
                pass

        # Method 2: Some models store it as m_Header
        if training_header is None and hasattr(classifier, 'm_Header'):
            try:
                training_header = classifier.m_Header
                IJ.log("Found m_Header in model")
            except:
                pass

        # Method 3: Some FilteredClassifiers wrap the header
        if training_header is None and hasattr(classifier, 'getClassifier'):
            try:
                base_classifier = classifier.getClassifier()
                if hasattr(base_classifier, 'getHeader'):
                    training_header = base_classifier.getHeader()
                    IJ.log("Found header in wrapped classifier")
                elif hasattr(base_classifier, 'm_Header'):
                    training_header = base_classifier.m_Header
                    IJ.log("Found m_Header in wrapped classifier")
            except:
                pass

        # Extract information from training header if available
        if training_header is not None:
            IJ.log("Extracting feature and class information from training header...")

            # Get feature names (all attributes except class)
            num_attributes = training_header.numAttributes()
            class_index = training_header.classIndex()

            IJ.log("Model expects {} total attributes (including class)".format(num_attributes))
            IJ.log("Class attribute is at index: {}".format(class_index))

            # Extract feature attribute names
            for i in range(num_attributes):
                attr = training_header.attribute(i)
                if i != class_index:  # Skip class attribute
                    feature_names.append(str(attr.name()))

            # Extract class labels
            if class_index >= 0:
                class_attr = training_header.classAttribute()
                if class_attr.isNominal():
                    for i in range(class_attr.numValues()):
                        class_labels.append(str(class_attr.value(i)))
                else:
                    IJ.log("Warning: Class attribute is not nominal (it's numeric)")

            # Log findings
            IJ.log("=== MODEL ANALYSIS RESULTS ===")
            IJ.log("Required features ({} total):".format(len(feature_names)))
            for i, name in enumerate(feature_names):
                IJ.log("  {}. {}".format(i+1, name))

            IJ.log("Class labels ({} total):".format(len(class_labels)))
            for i, label in enumerate(class_labels):
                IJ.log("  {}. {}".format(i+1, label))
            IJ.log("===============================")

            return feature_names, class_labels, True

        else:
            IJ.log("Could not extract training header from model")

            # Fallback: Try to analyze model string representation
            model_string = str(classifier)
            IJ.log("Attempting to parse model string representation...")

            # Some models include attribute info in their string representation
            if "Attribute" in model_string or "attributes" in model_string:
                IJ.log("Model string contains attribute information:")
                # Print relevant parts of the model string
                lines = model_string.split('\n')
                for line in lines[:20]:  # First 20 lines usually contain structure info
                    if any(keyword in line.lower() for keyword in ['attribute', 'class', 'feature']):
                        IJ.log("  " + line.strip())

            IJ.log("Manual configuration of REQUIRED_FEATURES and class labels is needed")
            return [], [], False

    except Exception as e:
        IJ.log("Error analyzing model: " + str(e))
        IJ.log("Manual configuration of REQUIRED_FEATURES and class labels is needed")
        return [], [], False

def auto_configure_from_model(feature_names, class_labels):
    """
    Update global configuration based on model analysis results.
    """
    global REQUIRED_FEATURES

    if feature_names:
        IJ.log("Auto-configuring REQUIRED_FEATURES from model...")
        REQUIRED_FEATURES = feature_names
        IJ.log("Updated REQUIRED_FEATURES: " + str(REQUIRED_FEATURES))
        return True
    else:
        IJ.log("Could not auto-configure features. Using manual REQUIRED_FEATURES configuration.")
        return False

def get_features_from_results_table(rt):
    """
    Extracts numerical features from ImageJ ResultsTable for machine learning.

    This function processes the ResultsTable to extract only numerical measurement
    columns, excluding spatial coordinates and labels that aren't useful for classification.

    Args:
        rt: ImageJ ResultsTable containing particle measurements

    Returns:
        tuple: (features_list, feature_headings) where:
            - features_list: List of feature vectors (each row = one particle)
            - feature_headings: List of column names corresponding to features
    """
    if rt is None or rt.getCounter() == 0:
        IJ.log("ERROR: No results table or empty table provided")
        return [], []

    num_rows = rt.getCounter()
    headings = rt.getHeadings()

    # Define columns to exclude from feature extraction
    # These are typically spatial coordinates or identifiers, not useful for classification
    EXCLUDED_COLUMNS = {
        "label", "id", "x", "y", "bx", "by", "width", "height",
        "xm", "ym", "xstart", "ystart", "xend", "yend"
    }

    # Filter to get only feature columns
    feature_headings = [h for h in headings if h.lower() not in EXCLUDED_COLUMNS]

    if not feature_headings:
        IJ.log("ERROR: No feature columns found in ResultsTable")
        IJ.log("  Available headings: " + str(list(headings)))
        IJ.log("  Excluded headings: " + str(EXCLUDED_COLUMNS))
        return [], []

    IJ.log("-> Extracting features from {} columns: {}".format(len(feature_headings), feature_headings))

    # Extract feature data with validation
    features_list = []
    invalid_rows = 0

    for row_idx in range(num_rows):
        row_features = []
        valid_row = True

        # Extract each feature value for this particle
        for heading in feature_headings:
            try:
                value = rt.getValue(heading, row_idx)

                # Validate numerical value
                if not _is_valid_number(value):
                    IJ.log("WARNING: Invalid value '{}' in '{}' at row {}".format(value, heading, row_idx))
                    valid_row = False
                    break

                row_features.append(float(value))

            except Exception as e:
                IJ.log("WARNING: Error reading '{}' at row {}: {}".format(heading, row_idx, e))
                valid_row = False
                break

        # Add row if all features are valid
        if valid_row and len(row_features) == len(feature_headings):
            features_list.append(row_features)
        else:
            invalid_rows += 1

    # Report extraction results
    valid_rows = len(features_list)
    IJ.log("SUCCESS: Feature extraction complete:")
    IJ.log("  - Valid particles: {} / {}".format(valid_rows, num_rows))
    if invalid_rows > 0:
        IJ.log("  - Skipped particles: {} (invalid data)".format(invalid_rows))

    return features_list, feature_headings


def _is_valid_number(value):
    """
    Helper function to validate if a value is a valid number for machine learning.

    Args:
        value: The value to check

    Returns:
        bool: True if value is a valid finite number, False otherwise
    """
    try:
        float_val = float(value)
        # Check for NaN, infinity, or other invalid values
        if str(float_val).lower() in ['nan', 'inf', '-inf', 'infinity', '-infinity']:
            return False
        return True
    except (ValueError, TypeError):
        return False

def create_weka_instances(particle_features_list, feature_names, class_labels=None):
    """Create Weka Instances object with proper attribute definitions"""

    # Create attributes list
    attributes = ArrayList()

    # Use the provided feature names (should already be ordered correctly)
    for name in feature_names:
        # Create numeric attribute
        attr = WekaAttribute(name.replace(" ", "_"))  # Replace spaces with underscores
        attributes.add(attr)
        IJ.log("Added numeric attribute: " + name)

    # Define class attribute (must be last)
    if class_labels is None or len(class_labels) == 0:
        # Use default class labels if none provided
        class_labels_list = ArrayList()
        for label in DEFAULT_CLASS_LABELS:
            class_labels_list.add(label)
        IJ.log("Using default class labels")
    else:
        # Use class labels from model analysis
        class_labels_list = ArrayList()
        for label in class_labels:
            class_labels_list.add(label)
        IJ.log("Using class labels from model: " + str(class_labels))

    class_attr = WekaAttribute("Label", class_labels_list)
    attributes.add(class_attr)

    # Create Instances object
    instances = WekaInstances("ParticleData", attributes, len(particle_features_list))
    instances.setClassIndex(instances.numAttributes() - 1)

    # Add data instances
    for feature_vector in particle_features_list:
        if len(feature_vector) != (instances.numAttributes() - 1):
            IJ.log("Skipping row: feature count mismatch")
            continue

        # Create instance
        instance = WekaDenseInstance(instances.numAttributes())
        instance.setDataset(instances)

        # Set feature values
        for i, value in enumerate(feature_vector):
            try:
                instance.setValue(i, float(value))
            except Exception as e:
                IJ.log("Error setting value: " + str(e))
                instance = None
                break

        if instance is not None:
            instance.setClassMissing()  # Class is unknown for prediction
            instances.add(instance)

    IJ.log("Created {} Weka instances for classification".format(instances.numInstances()))
    return instances

def validate_and_order_features(particle_features_list, feature_names, required_features):
    """
    Validates that all required features are present, reorders them to match model expectations,
    and removes any unnecessary features.

    Args:
        particle_features_list: List of feature vectors (each vector is a list of values)
        feature_names: List of feature names from the results table
        required_features: List of required feature names in the correct order for the model

    Returns:
        tuple: (ordered_features_list, ordered_feature_names, success_flag)
    """
    IJ.log("Validating and ordering features for model compatibility...")

    # Check if all required features are present
    missing_features = []
    for required_feature in required_features:
        if required_feature not in feature_names:
            missing_features.append(required_feature)

    if missing_features:
        IJ.log("ERROR: Missing required features for the model:")
        for missing in missing_features:
            IJ.log("  - " + missing)
        IJ.log("Available features: " + str(feature_names))
        IJ.log("Required features: " + str(required_features))
        return [], [], False

    # Create mapping from feature name to index in original data
    feature_index_map = {}
    for i, name in enumerate(feature_names):
        feature_index_map[name] = i

    # Get indices of required features in the correct order
    required_indices = []
    for required_feature in required_features:
        required_indices.append(feature_index_map[required_feature])

    IJ.log("Feature mapping:")
    for i, required_feature in enumerate(required_features):
        original_index = required_indices[i]
        IJ.log("  {} (position {}) -> {} (position {})".format(
            feature_names[original_index], original_index, required_feature, i))

    # Reorder and filter features for each particle
    ordered_features_list = []

    for particle_features in particle_features_list:
        if len(particle_features) != len(feature_names):
            IJ.log("Warning: Particle has {} features but expected {}. Skipping.".format(
                len(particle_features), len(feature_names)))
            continue

        # Extract only the required features in the correct order
        ordered_particle_features = []
        for required_index in required_indices:
            ordered_particle_features.append(particle_features[required_index])

        ordered_features_list.append(ordered_particle_features)

    # Log summary
    original_feature_count = len(feature_names)
    required_feature_count = len(required_features)
    original_particle_count = len(particle_features_list)
    final_particle_count = len(ordered_features_list)

    IJ.log("Feature validation and ordering complete:")
    IJ.log("  - Reduced features from {} to {} (removed {} unnecessary features)".format(
        original_feature_count, required_feature_count,
        original_feature_count - required_feature_count))
    IJ.log("  - Processed {} out of {} particles successfully".format(
        final_particle_count, original_particle_count))

    if original_feature_count > required_feature_count:
        removed_features = [f for f in feature_names if f not in required_features]
        IJ.log("  - Removed features: " + str(removed_features))

    return ordered_features_list, required_features, True

def color_particles(predictions, color_map):
    """Color particles based on predictions"""
    imp = WindowManager.getCurrentImage()
    if imp is None:
        IJ.log("No image open to draw ROIs on.")
        return

    roim = RoiManager.getInstance()
    if roim is None:
        roim = RoiManager(True)

    if roim.getCount() == 0:
        IJ.log("ROI Manager is empty. Cannot color particles.")
        return

    num_rois = roim.getCount()
    num_predictions = len(predictions)
    num_to_color = min(num_rois, num_predictions)

    if num_to_color == 0:
        IJ.log("Nothing to color.")
        return

    IJ.log("Coloring {} ROIs based on predictions...".format(num_to_color))

    # Ensure image has overlay
    overlay = imp.getOverlay()
    if overlay is None:
        from ij.gui import Overlay
        overlay = Overlay()
        imp.setOverlay(overlay)

    roim.runCommand("Deselect All")

    for i in range(num_to_color):
        roi = roim.getRoi(i)
        predicted_class = predictions[i]

        color_to_use = color_map.get(predicted_class, Color.GRAY)

        roi.setFillColor(color_to_use)
        roi.setStrokeColor(color_to_use)

    roim.runCommand("Show All with labels")
    imp.updateAndDraw()

    IJ.log("Finished coloring {} ROIs".format(num_to_color))

def batch_open_images(path, file_type=None, name_filter=None):
    """
    Open all files in the given folder that match the specified criteria.

    This function uses the same approach as the working implementation:
    - Direct Unicode filename handling without conversion
    - Simple string operations on original filenames
    - Minimal logging to avoid encoding issues

    Args:
        path: Directory path to scan (string or java.io.File)
        file_type: File extensions to include (string, list, or tuple)
        name_filter: Filename patterns to include (string, list, or tuple)

    Returns:
        list: Full paths to matching image files
    """
    # Convert File object to string path if needed
    if isinstance(path, File):
        path = path.getAbsolutePath()

    def check_type(string):
        """Check if filename matches any of the specified file types."""
        if file_type:
            # Handle list/tuple of file types
            if isinstance(file_type, (list, tuple)):
                for file_type_ in file_type:
                    if string.endswith(file_type_):
                        return True
                    else:
                        continue
            # Handle single string file type
            elif isinstance(file_type, str):
                if string.endswith(file_type):
                    return True
                else:
                    return False
            return False
        # Accept all files if file_type is None
        else:
            return True

    def check_filter(string):
        """Check if filename contains any of the specified filter strings."""
        if name_filter:
            # Handle list/tuple of name filters
            if isinstance(name_filter, (list, tuple)):
                for name_filter_ in name_filter:
                    if name_filter_ in string:
                        return True
                    else:
                        continue
            # Handle single string name filter
            elif isinstance(name_filter, str):
                if name_filter in string:
                    return True
                else:
                    return False
            return False
        else:
            # Accept all files if name_filter is None
            return True

    # Collect all matching files
    path_to_images = []

    # Expand environment variables and user home directory
    path = os.path.expanduser(path)
    path = os.path.expandvars(path)

    # Scan directory for matching files
    try:
        for file_name in os.listdir(path):
            full_path = os.path.join(path, file_name)
            if os.path.isfile(full_path):
                if check_type(file_name):
                    if check_filter(file_name):
                        path_to_images.append(full_path)
    except Exception as e:
        IJ.log("ERROR: Error scanning directory: " + str(e))
        return []

    return path_to_images

def split_string(input_string):
    '''Split a string to a list and strip it
    :param input_string: A string that contains semicolons as separators.
    '''
    string_splitted = input_string.split(';')
    # Remove whitespace at the beginning and end of each string
    strings_striped = [string.strip() for string in string_splitted]
    return strings_striped


# === MAIN SCRIPT ===
def main():
    """
    Main function that orchestrates the entire batch processing workflow.

    This function coordinates all the steps needed for batch particle analysis:
    1. Setup and validation
    2. Weka library initialization
    3. Image discovery and processing
    4. Results compilation and export
    5. Cleanup
    """
    # Clear log and start fresh
    IJ.log("\\Clear")
    IJ.log("=" * 60)
    IJ.log("FIJI/ImageJ Batch Particle Classification Script")
    IJ.log("=" * 60)

    # Record start time for performance measurement
    start_time = time.time()
    IJ.log("Processing started at: {}".format(time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(start_time))))

    try:
        # === INITIALIZATION PHASE ===
        IJ.log("\n[INITIALIZATION PHASE]")

        # Step 1: Ensure Weka is available
        IJ.log("-> Checking Weka availability...")
        if not ensure_weka_in_classpath():
            IJ.log("ERROR: Failed to ensure Weka is in classpath")
            return False

        # Step 2: Import Weka classes
        IJ.log("-> Importing Weka classes...")
        if not import_weka_classes():
            IJ.log("ERROR: Failed to import Weka classes")
            return False

        # Step 3: Test Weka functionality
        IJ.log("-> Testing Weka functionality...")
        if not test_weka_functionality():
            IJ.log("ERROR: Weka functionality test failed")
            return False

        # Step 4: Validate configuration
        IJ.log("-> Validating configuration...")
        if not validate_configuration():
            IJ.log("ERROR: Configuration validation failed")
            return False

        # Step 5: Load Weka model once at startup (PERFORMANCE OPTIMIZATION)
        IJ.log("-> Loading Weka classification model...")
        global GLOBAL_CLASSIFIER, GLOBAL_MODEL_FEATURES, GLOBAL_MODEL_CLASSES
        GLOBAL_CLASSIFIER = load_weka_model(MODEL_PATH)
        if GLOBAL_CLASSIFIER is None:
            IJ.log("ERROR: Failed to load Weka model")
            return False

        # Step 6: Analyze model once at startup (PERFORMANCE OPTIMIZATION)
        IJ.log("-> Analyzing model structure...")
        GLOBAL_MODEL_FEATURES, GLOBAL_MODEL_CLASSES, analysis_success = analyze_weka_model(GLOBAL_CLASSIFIER)

        # Auto-configure if model analysis was successful
        if analysis_success and GLOBAL_MODEL_FEATURES:
            IJ.log("-> Auto-configuring from model...")
            auto_configure_from_model(GLOBAL_MODEL_FEATURES, GLOBAL_MODEL_CLASSES)

        # === IMAGE DISCOVERY PHASE ===
        IJ.log("\n[IMAGE DISCOVERY PHASE]")

        path_to_images = batch_open_images(IMPORT_DIR,
                                split_string(FILE_TYPES),
                                split_string(FILTERS))

        if not path_to_images:
            IJ.log("ERROR: No images found to process")
            return False

        IJ.log("SUCCESS: Found {} images to process".format(len(path_to_images)))

        # Provide time estimate for user planning
        time_estimate = estimate_processing_time(len(path_to_images))
        IJ.log("-> Estimated processing time: {}".format(time_estimate['formatted']))

        # Check parallel processing capability (future enhancement)
        parallel_ready = prepare_parallel_processing()
        if parallel_ready:
            IJ.log("-> Parallel processing enabled with {} threads".format(MAX_WORKER_THREADS))
        else:
            IJ.log("-> Using single-threaded processing")

        # === PROCESSING PHASE ===
        IJ.log("\n" + "=" * 60)
        IJ.log("[PROCESSING PHASE]")
        IJ.log("=" * 60)

        # Initialize checkpoint system
        checkpoint_path = os.path.join(os.path.dirname(IMPORT_DIR), CHECKPOINT_FILE)
        checkpoint_data = None
        processed_images = set()

        # Try to load existing checkpoint for resume capability
        if ENABLE_RESUME:
            checkpoint_data = load_checkpoint(checkpoint_path)
            if checkpoint_data:
                processed_images = set(checkpoint_data.get('processed_images', []))
                IJ.log("-> Resume mode: {} images already processed".format(len(processed_images)))

        # Initialize statistics tracking
        prediction_stats = dict(zip(DEFAULT_CLASS_LABELS, [0] * len(DEFAULT_CLASS_LABELS)))
        all_image_predictions = []
        processed_count = 0
        failed_count = 0

        # Restore statistics from checkpoint if resuming
        if checkpoint_data:
            prediction_stats.update(checkpoint_data.get('prediction_stats', {}))
            all_image_predictions = checkpoint_data.get('all_image_predictions', [])
            processed_count = checkpoint_data.get('processed_count', 0)
            failed_count = checkpoint_data.get('failed_count', 0)

        # Process each image with enhanced error handling and resume capability
        consecutive_failures = 0
        max_consecutive_failures = 5  # Stop batch if too many consecutive failures

        for img_idx, img_path in enumerate(path_to_images):
            img_name = os.path.basename(img_path)

            # Skip already processed images if resuming
            if SKIP_PROCESSED_IMAGES and img_name in processed_images:
                IJ.log("-> Skipping already processed image: {}".format(img_idx + 1))
                IJ.log("  " + "." * 30 + " SKIPPED " + "." * 30)
                continue

            # Enhanced progress logging with particle count information
            if img_idx % LOG_PROGRESS_INTERVAL == 0 or img_idx == len(path_to_images) - 1:
                progress_pct = (img_idx + 1) * 100.0 / len(path_to_images)
                total_particles = sum(prediction_stats.values())
                remaining_images = len(path_to_images) - len(processed_images)
                IJ.log("-> Progress: {:.1f}% ({}/{}) - {} particles classified, {} images remaining".format(
                    progress_pct, img_idx + 1, len(path_to_images), total_particles, remaining_images))

            # Load and process image with enhanced error handling
            try:
                current_img = IJ.openImage(img_path)
                if current_img is None:
                    IJ.log("WARNING: Failed to open image")
                    failed_count += 1
                    consecutive_failures += 1

                    # Check for too many consecutive failures
                    if consecutive_failures >= max_consecutive_failures:
                        IJ.log("ERROR: {} consecutive failures detected. Aborting batch processing.".format(consecutive_failures))
                        IJ.log("This may indicate a critical issue with the macro or configuration.")
                        return False
                    continue

                # Ensure image is properly registered with ImageJ for macro execution
                # This is critical: macros require the image to be active in WindowManager
                if SHOW_IMAGES_DURING_PROCESSING:
                    # Visual monitoring mode: display image normally
                    current_img.show()
                    IJ.log("  -> Image displayed for visual monitoring")
                else:
                    # Background mode: register image for macro execution without visual display
                    try:
                        # Method 1: Show and immediately hide the window
                        current_img.show()  # Register with WindowManager
                        window = current_img.getWindow()
                        if window is not None:
                            window.setVisible(False)  # Hide the window immediately
                            IJ.log("  -> Image registered for macro execution (window hidden)")
                        else:
                            IJ.log("  -> Image registered for macro execution (no window created)")
                    except Exception as e:
                        # Method 2: Fallback - just show the image (macro compatibility is priority)
                        IJ.log("  WARNING: Could not hide window, using visible mode for macro compatibility: " + str(e))
                        current_img.show()
                        IJ.log("  -> Image displayed (fallback for macro compatibility)")

                predictions = process_picture(current_img, GLOBAL_CLASSIFIER, GLOBAL_MODEL_FEATURES, GLOBAL_MODEL_CLASSES)

                if predictions:
                    # Reset consecutive failure counter on success
                    consecutive_failures = 0

                    # Compile statistics for this image
                    img_stats = dict(zip(DEFAULT_CLASS_LABELS, [0] * len(DEFAULT_CLASS_LABELS)))
                    particle_count_this_image = 0

                    for pred in predictions:
                        if pred in img_stats:
                            img_stats[pred] += 1
                            prediction_stats[pred] += 1
                            particle_count_this_image += 1
                        else:
                            IJ.log("WARNING: Unknown prediction class encountered")

                    # Store results with original filename for CSV export
                    all_image_predictions.append((img_name, img_stats))
                    processed_count += 1
                    processed_images.add(img_name)  # Track processed images for resume

                    IJ.log("  -> Processed {} particles in current image".format(particle_count_this_image))

                    # Save checkpoint periodically for resume capability
                    if ENABLE_RESUME and (processed_count % CHECKPOINT_INTERVAL == 0):
                        checkpoint_data = {
                            'processed_images': list(processed_images),
                            'prediction_stats': prediction_stats,
                            'all_image_predictions': all_image_predictions,
                            'processed_count': processed_count,
                            'failed_count': failed_count,
                            'timestamp': time.time() if 'time' in globals() else 0
                        }
                        save_checkpoint(checkpoint_data, checkpoint_path)

                else:
                    IJ.log("WARNING: No predictions made for current image")
                    failed_count += 1
                    consecutive_failures += 1

            except Exception as e:
                IJ.log("ERROR: Critical error processing current image: " + str(e))
                failed_count += 1
                consecutive_failures += 1

                # Check for too many consecutive failures
                if consecutive_failures >= max_consecutive_failures:
                    IJ.log("ERROR: {} consecutive failures detected. Aborting batch processing.".format(consecutive_failures))
                    return False

            # Add clear visual separation after each image processing
            IJ.log("  " + "-" * 50)

        # === RESULTS PHASE ===
        IJ.log("\n" + "=" * 60)
        IJ.log("[RESULTS PHASE]")
        IJ.log("=" * 60)

        IJ.log("SUCCESS: Processing complete:")
        IJ.log("  - Successfully processed: {} images".format(processed_count))
        if failed_count > 0:
            IJ.log("  - Failed to process: {} images".format(failed_count))

        # Display classification statistics
        total_particles = sum(prediction_stats.values())
        IJ.log("\n[CLASSIFICATION STATISTICS]")
        for label, count in prediction_stats.items():
            percentage = (count * 100.0 / total_particles) if total_particles > 0 else 0
            IJ.log("  - {}: {} particles ({:.1f}%)".format(label, count, percentage))

        # === EXPORT PHASE ===
        IJ.log("\n" + "=" * 60)
        IJ.log("[EXPORT PHASE]")
        IJ.log("=" * 60)

        if all_image_predictions:
            export_success = _export_results_to_csv(all_image_predictions, prediction_stats)
            if export_success:
                IJ.log("SUCCESS: Results exported successfully")
            else:
                IJ.log("WARNING: Results export failed")
        else:
            IJ.log("WARNING: No results to export")

        # === CLEANUP PHASE ===
        if CLEANUP_ON_EXIT:
            IJ.log("\n[CLEANUP PHASE]")
            _perform_cleanup()

        # === COMPLETION ===
        # Record end time and calculate performance metrics
        end_time = time.time()
        total_particles = sum(prediction_stats.values())

        # Clear checkpoint file after successful completion
        if ENABLE_RESUME:
            clear_checkpoint(checkpoint_path)

        # Display comprehensive performance summary
        log_performance_summary(start_time, end_time, processed_count, total_particles)

        IJ.log("\n" + "=" * 60)
        IJ.log("SUCCESS: BATCH PROCESSING COMPLETED SUCCESSFULLY")
        IJ.log("Processing completed at: {}".format(time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(end_time))))
        IJ.log("=" * 60)
        return True

    except Exception as e:
        # Record end time even for failed processing
        end_time = time.time()
        elapsed_time = format_elapsed_time(end_time - start_time)

        IJ.log("\n" + "=" * 60)
        IJ.log("ERROR: BATCH PROCESSING FAILED")
        IJ.log("Error: " + str(e))
        IJ.log("Processing time before failure: {}".format(elapsed_time))
        IJ.log("Failed at: {}".format(time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(end_time))))
        IJ.log("=" * 60)
        return False


def _export_results_to_csv(all_image_predictions, prediction_stats):
    """
    Exports classification results to a CSV file using direct Unicode handling.

    This function uses the working implementation approach - direct Unicode
    filename handling without any conversion functions.

    Args:
        all_image_predictions: List of (image_name, stats_dict) tuples
        prediction_stats: Overall statistics dictionary

    Returns:
        bool: True if export successful, False otherwise
    """
    csv_path = os.path.join(os.path.dirname(IMPORT_DIR), "particle_predictions.csv")
    writer = None

    try:
        IJ.log("-> Exporting results to CSV file...")

        # Use simple FileWriter approach like the working implementation
        writer = BufferedWriter(FileWriter(File(csv_path)))

        # Write CSV header
        header_parts = ['Image'] + [label for label in DEFAULT_CLASS_LABELS]
        header = ','.join(header_parts) + '\n'
        writer.write(header)

        # Write predictions for each image using original Unicode filenames
        for img_name, img_stats in all_image_predictions:
            row_values = [img_name] + [str(img_stats.get(label, 0)) for label in DEFAULT_CLASS_LABELS]
            row = ','.join(row_values) + '\n'
            writer.write(row)

        # Write total row
        total_values = ['TOTAL'] + [str(prediction_stats.get(label, 0)) for label in DEFAULT_CLASS_LABELS]
        total_row = ','.join(total_values) + '\n'
        writer.write(total_row)

        # Write percentage row
        total_sum = sum(prediction_stats.values())
        if total_sum > 0:
            percentage_values = ['TOTAL (%)'] + [
                "{:.2f}%".format(prediction_stats.get(label, 0) * 100.0 / total_sum)
                for label in DEFAULT_CLASS_LABELS
            ]
            percentage_row = ','.join(percentage_values) + '\n'
            writer.write(percentage_row)

        IJ.log("SUCCESS: CSV export completed successfully")
        return True

    except Exception as e:
        IJ.log("ERROR: Error writing CSV file: " + str(e))
        return False

    finally:
        if writer:
            try:
                writer.close()
            except:
                pass


def _perform_cleanup():
    """
    Performs cleanup operations to close windows and clear memory.

    This function closes all open images, clears the ResultsTable,
    and resets the RoiManager to free up memory and clean the workspace.
    """
    try:
        # Close all open images
        ids = WindowManager.getIDList()
        if ids is not None:
            IJ.log("-> Closing {} open images...".format(len(ids)))
            for id in ids:
                imp = WindowManager.getImage(id)
                if imp is not None:
                    imp.changes = False  # Prevent "Save Changes?" dialog
                    imp.close()
        else:
            IJ.log("-> No images to close")

        # Clear ResultsTable
        rt = ResultsTable.getResultsTable()
        if rt is not None:
            rt.reset()
            rt.show("Results")  # Update the window
            IJ.selectWindow("Results")  # Select the Results window
            IJ.run("Close")  # Close the window
            IJ.log("-> ResultsTable cleared")
        else:
            IJ.log("-> No ResultsTable to clear")

        # Clear RoiManager
        roim = RoiManager.getInstance()
        if roim is not None:
            roim.reset()
            roim.close()  # Close the RoiManager window
            IJ.log("-> RoiManager cleared")
        else:
            IJ.log("-> No RoiManager to clear")

        IJ.log("SUCCESS: Cleanup completed")

    except Exception as e:
        IJ.log("WARNING: Error during cleanup: " + str(e))

def _run_macro_and_extract_features(imp):
    """
    Runs the ImageJ macro and extracts features from the results table.

    Args:
        imp: ImagePlus object representing the image to process

    Returns:
        tuple: (particle_features, feature_names, particle_count) or (None, None, 0) if failed
    """
    # CRITICAL: Use original image title directly without Unicode conversion
    image_title = imp.getTitle()

    # === ENSURE IMAGE IS ACTIVE FOR MACRO EXECUTION ===
    # This is essential: ImageJ macros require an active image in the WindowManager
    try:
        # Set this image as the current/active image for macro execution
        WindowManager.setCurrentWindow(imp.getWindow())
        IJ.log("  -> Image set as active for macro execution")
    except Exception as e:
        IJ.log("  WARNING: Could not set image as active: " + str(e))
        # Continue anyway - the image might still work

    # === MACRO EXECUTION ===
    IJ.log("  -> Running analysis macro...")
    IJ.runMacroFile(MACRO_PATH, image_title)

    # === RESULTS EXTRACTION ===
    rt = ResultsTable.getResultsTable()
    if rt is None or rt.getCounter() == 0:
        IJ.log("  ERROR: No results generated by macro")
        return None, None, 0

    particle_count = rt.getCounter()
    IJ.log("  -> Found {} particles to analyze".format(particle_count))

    # === FEATURE EXTRACTION ===
    IJ.log("  -> Extracting particle features...")
    particle_features, feature_names = get_features_from_results_table(rt)
    if not particle_features:
        IJ.log("  ERROR: No features extracted")
        return None, None, particle_count

    return particle_features, feature_names, particle_count


def _validate_and_prepare_features(particle_features, feature_names):
    """
    Validates and orders features for model compatibility.

    Args:
        particle_features: List of feature vectors
        feature_names: List of feature names

    Returns:
        tuple: (ordered_features, ordered_feature_names) or (None, None) if failed
    """
    # === FEATURE VALIDATION ===
    IJ.log("  -> Validating feature compatibility...")
    ordered_features, ordered_feature_names, validation_success = validate_and_order_features(
        particle_features, feature_names, REQUIRED_FEATURES)

    if not validation_success:
        IJ.log("  ERROR: Feature validation failed")
        return None, None

    if not ordered_features:
        IJ.log("  ERROR: No valid features after validation")
        return None, None

    return ordered_features, ordered_feature_names


def _classify_particles(ordered_features, ordered_feature_names, classifier, model_classes):
    """
    Creates Weka instances and performs classification.

    Args:
        ordered_features: List of ordered feature vectors
        ordered_feature_names: List of ordered feature names
        classifier: Loaded Weka classifier
        model_classes: Class labels from model analysis

    Returns:
        list: Classification predictions or None if failed
    """
    # === WEKA INSTANCE CREATION ===
    IJ.log("  -> Creating Weka instances...")
    instances = create_weka_instances(ordered_features, ordered_feature_names, model_classes)
    if instances.numInstances() == 0:
        IJ.log("  ERROR: No valid instances created")
        return None

    # === CLASSIFICATION ===
    IJ.log("  -> Classifying {} particles...".format(instances.numInstances()))
    predictions = []

    for i in range(instances.numInstances()):
        instance = instances.instance(i)
        prediction_index = classifier.classifyInstance(instance)
        predicted_label = instances.classAttribute().value(int(prediction_index))
        predictions.append(predicted_label)

    IJ.log("  -> Classification complete: {} predictions made".format(len(predictions)))
    return predictions


def _visualize_results(predictions, model_classes):
    """
    Applies color coding to particles based on classification results.

    Args:
        predictions: List of classification predictions
        model_classes: Class labels from model analysis
    """
    # === VISUALIZATION ===
    IJ.log("  -> Applying color coding...")
    class_labels = DEFAULT_CLASS_LABELS if not model_classes else model_classes
    colors = PARTICLE_COLORS[:len(class_labels)]
    color_map = dict(zip(class_labels, colors))
    color_particles(predictions, color_map)


def process_picture(imp, classifier, model_features, model_classes):
    """
    Processes a single image through the complete analysis pipeline.

    This function coordinates the complete workflow for one image using pre-loaded
    model components for optimal performance.

    Args:
        imp: ImagePlus object representing the image to process
        classifier: Pre-loaded Weka classifier
        model_features: Pre-analyzed model features
        model_classes: Pre-analyzed model class labels

    Returns:
        list: Classification predictions for each particle, or None if processing failed
    """
    if imp is None:
        IJ.log("ERROR: Invalid image provided")
        return None

    image_title = imp.getTitle()
    IJ.log("Processing image: " + image_title)

    try:
        # Step 1: Run macro and extract features
        particle_features, feature_names, particle_count = _run_macro_and_extract_features(imp)
        if particle_features is None:
            return None

        # Step 2: Validate and prepare features
        ordered_features, ordered_feature_names = _validate_and_prepare_features(particle_features, feature_names)
        if ordered_features is None:
            return None

        # Step 3: Classify particles using pre-loaded model
        predictions = _classify_particles(ordered_features, ordered_feature_names, classifier, model_classes)
        if predictions is None:
            return None

        # Step 4: Visualize results
        _visualize_results(predictions, model_classes)

        IJ.log("  SUCCESS: Image processing completed successfully")
        return predictions

    except Exception as e:
        IJ.log("  ERROR: Error processing image: " + str(e))
        return None

# === SCRIPT ENTRY POINT ===
if __name__ == '__main__':
    """
    Script entry point - executes when the script is run directly.

    This is the main execution block that starts the batch processing workflow.
    The script can be run from FIJI/ImageJ's script editor or from the command line.
    """
    script_start_time = time.time()
    try:
        success = main()
        script_end_time = time.time()
        total_script_time = format_elapsed_time(script_end_time - script_start_time)

        if success:
            IJ.log("\nSUCCESS: Script execution completed successfully!")
            IJ.log("Total script runtime: {}".format(total_script_time))
        else:
            IJ.log("\nERROR: Script execution failed. Check the log for details.")
            IJ.log("Script runtime before failure: {}".format(total_script_time))
    except Exception as e:
        script_end_time = time.time()
        total_script_time = format_elapsed_time(script_end_time - script_start_time)

        IJ.log("\nERROR: Unexpected error during script execution:")
        IJ.log("Error: " + str(e))
        IJ.log("Script runtime before error: {}".format(total_script_time))
        IJ.log("Please check your configuration and try again.")