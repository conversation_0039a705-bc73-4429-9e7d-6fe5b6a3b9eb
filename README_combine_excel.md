# Excel File Combiner

This Python script combines multiple Excel files containing particle analysis data by worksheets and creates comprehensive summaries.

## Features

- **Worksheet-wise combination**: Combines data from worksheets with the same name across multiple files
- **Total summary**: Creates a comprehensive summary containing all data from all worksheets
- **Source tracking**: Adds columns to track which file and worksheet each row originated from
- **Automatic filtering**: Excludes previously generated combined files and summary sheets
- **Robust handling**: Handles missing worksheets, empty sheets, and various Excel formats

## Requirements

- Python 3.6+
- pandas
- openpyxl

Install dependencies:
```bash
pip install pandas openpyxl
```

## Usage

### Command Line
```bash
# Specify folder path directly
python combine_excel_files.py /path/to/excel/files

# Interactive mode (will prompt for folder)
python combine_excel_files.py
```

### Expected Input Structure

The script expects Excel files with worksheets named according to the pattern:
- `{Shape}_{Porosity}` (e.g., Round_NonPorous, Satellite_Porous, Splattered_NonPorous)
- Summary sheets are automatically skipped

Example input files:
```
folder/
├── file1.xlsx
│   ├── Round_NonPorous
│   ├── Round_Porous
│   ├── Satellite_NonPorous
│   ├── Satellite_Porous
│   ├── Splattered_NonPorous
│   ├── Splattered_Porous
│   └── Summary (skipped)
├── file2.xlsx
│   ├── Round_NonPorous
│   ├── Satellite_Porous
│   └── Summary (skipped)
└── file3.xlsx
    ├── Round_Porous
    ├── Splattered_NonPorous
    └── Summary (skipped)
```

### Output Structure

The script creates a combined Excel file with:

1. **Individual worksheet combinations**: One sheet for each unique worksheet type found
   - `Round_NonPorous`: All Round_NonPorous data from all files
   - `Round_Porous`: All Round_Porous data from all files
   - `Satellite_NonPorous`: All Satellite_NonPorous data from all files
   - etc.

2. **Total_Summary sheet**: All data combined with enhanced classification columns
   - `Shape`: Particle shape (Round, Satellite, Splattered)
   - `Porosity`: Porosity classification (Porous, NonPorous)
   - Note: `Particle_ID` and `Source_File` columns are excluded for cleaner analysis

### Example Output

```
combined_excel_20250605_122253.xlsx
├── Round_NonPorous (32 rows from 4 files)
├── Round_Porous (6 rows from 3 files)
├── Satellite_NonPorous (30 rows from 4 files)
├── Satellite_Porous (8 rows from 4 files)
├── Splattered_NonPorous (7 rows from 4 files)
├── Splattered_Porous (16 rows from 4 files)
└── Total_Summary (99 rows total with shape/porosity classification)
```

## Features in Detail

### Data Enhancement and Column Management
**Individual worksheets** retain full tracking information:
- `Source_File`: The original Excel filename
- `Particle_ID`: Original particle identifier
- `Shape`: Particle shape extracted from worksheet name (Round, Satellite, Splattered)
- `Porosity`: Porosity classification extracted from worksheet name (Porous, NonPorous)

**Total_Summary sheet** provides clean analysis data:
- `Shape`: Particle shape (Round, Satellite, Splattered)
- `Porosity`: Porosity classification (Porous, NonPorous)
- `Particle_ID` and `Source_File` columns are excluded for streamlined analysis

The original `Group` column is automatically split into separate `Shape` and `Porosity` columns for better data analysis.

### Automatic Filtering
- Skips files starting with "combined_" to avoid processing previous outputs
- Skips worksheets containing "summary" in the name
- Handles empty worksheets gracefully

### Error Handling
- Continues processing if individual worksheets fail to load
- Provides detailed progress information
- Shows file contribution statistics

### Output Naming
Files are named with timestamp to avoid conflicts:
`combined_{folder_name}_{timestamp}.xlsx`

## Example Run

```
Found 4 Excel files:
  - V1-63µm-pol-125x-2.xlsx
  - V1-63µm-pol-125x-3.xlsx
  - V1-63µm-pol-125x-4.xlsx
  - V1-63µm-pol-125x-5.xlsx

Processing file: V1-63µm-pol-125x-2.xlsx
  Loaded sheet 'Round_NonPorous' with 10 rows
  Loaded sheet 'Round_Porous' with 1 rows
  ...

Combining worksheets...
  Combined 'Round_NonPorous': 32 total rows from 4 files
  Combined 'Round_Porous': 6 total rows from 3 files
  ...

============================================================
COMBINATION COMPLETE
============================================================
Input folder: .
Files processed: 4
Worksheet types found: 6
Output file: .\combined_excel_20250605_122253.xlsx

File contribution breakdown:
  V1-63µm-pol-125x-4.xlsx: 30 rows
  V1-63µm-pol-125x-2.xlsx: 29 rows
  V1-63µm-pol-125x-3.xlsx: 28 rows
  V1-63µm-pol-125x-5.xlsx: 12 rows
```
