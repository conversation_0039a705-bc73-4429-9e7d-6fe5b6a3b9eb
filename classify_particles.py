# Jython script for ImageJ: Run Macro, Classify with Weka, and Color Particles

from ij import IJ, WindowManager
from ij.measure import ResultsTable
from ij.plugin.frame import RoiManager
from java.io import FileInputStream
from java.awt import Color
import sys # For basic system ops, though not strictly used for exit here
import os

from weka.core import *
from weka.classifiers import *
from java.util import *
import weka.core.Instances as Instances

#from weka.core import *

from java.io import File
from java.net import URL
from java.lang import ClassLoader, Class, System

from java.util import ArrayList # For creating lists of attributes and nominal values


# --- Configuration ---
macro_path = "C:/Users/<USER>/Fiji.app/macros/extract_ASC_for_classification.ijm"  # Name of your macro file (if in macros folder) or full path
# IMPORTANT: Provide the FULL, ABSOLUTE path to your Weka model file
model_path = "C:/Studium/Johann/Partikeluntersuchungen/63um/perceptron_3_classes_ASC.model" # e.g., "D:/models/my_mlp_model.model"
USER_WEKA_JAR_PATH = "C:\Program Files\Weka-3-8-6\weka.jar"
# Define colors for the three classes (adjust as needed)
color_class1 = Color(255,0,0,100)
color_class2 = Color(0,255,0,100)
color_class3 = Color(0,0,255,100)

print("Python path:")
for p in sys.path:
    print(p)

try:
    import weka
    print("Weka module found at:", weka.__file__)
except:
    print("Weka module not found")

try:
    from weka import core
    print("Weka core available")
    print("Core contents:", dir(core))
except Exception as e:
    print("Weka core import failed:", e)


# --- Helper Functions ---

# === Function to check if Weka is already in classpath ===
# FIXED VERSION of ensure_weka_in_classpath()
def ensure_weka_in_classpath():
    """
    Checks if Weka classes can be imported and attempts to add Weka JAR to classpath if needed.
    """
    try:
        Class.forName("weka.classifiers.Classifier")        
        IJ.log("Weka libraries successfully found in ImageJ classpath.")
        return True
    except ImportError as e:
        IJ.log("Weka libraries not found: " + str(e))
        
        # Determine which path to use
        path_to_try = USER_WEKA_JAR_PATH
        
        if not path_to_try:
            IJ.log("No path for weka.jar configured. Please set USER_WEKA_JAR_PATH.")
            return False

        if not os.path.exists(path_to_try):
            IJ.log("Weka JAR not found at: " + path_to_try)
            return False

        IJ.log("Attempting to add Weka to classpath from: " + path_to_try)
        try:
            f = File(path_to_try)
            url = f.toURI().toURL()
            sys_loader = ClassLoader.getSystemClassLoader()
            method = sys_loader.getClass().getDeclaredMethod("addURL", [URL])
            method.setAccessible(True)
            method.invoke(sys_loader, [url])
            IJ.log("Weka jar added to classpath.")
            return True
        except (ImportError, Exception) as e:
            IJ.log("Failed to load Weka after adding to classpath: " + str(e))
            IJ.log("You may need to restart ImageJ for classpath changes to take effect.")
            return False
        

def test_weka_imports():
    try:
        #from weka.core import Instances, DenseInstance, SerializationHelper
        #from weka.core import Attribute as WekaAttribute
        from weka.core import *
        from weka.classifiers import AbstractClassifier
        from java.util import ArrayList
        IJ.log("All Weka imports successful!")
        return True
    except ImportError as e:
        IJ.log("Weka import failed: " + str(e))
        return False

        
def validate_configuration():
    """Validate all configuration settings before running main script"""
    errors = []
    warnings = []
    
    # Check macro file
    if not os.path.exists(macro_path):
        errors.append("Macro file not found: " + macro_path)
    
    # Check model file
    if not os.path.exists(model_path):
        errors.append("Model file not found: " + model_path)
    
    # Check Weka JAR if specified
    if USER_WEKA_JAR_PATH and not os.path.exists(USER_WEKA_JAR_PATH):
        warnings.append("Weka JAR not found at: " + USER_WEKA_JAR_PATH)
    
    # Check if an image is open
    from ij import WindowManager
    if WindowManager.getCurrentImage() is None:
        warnings.append("No image is currently open. Macro may fail if it expects an active image.")
    
    # Report results
    if errors:
        IJ.log("CONFIGURATION ERRORS:")
        for error in errors:
            IJ.log("  - " + error)
        return False
    
    if warnings:
        IJ.log("CONFIGURATION WARNINGS:")
        for warning in warnings:
            IJ.log("  - " + warning)
    
    IJ.log("Configuration validation passed.")
    return True


def load_weka_model(model_path):
    """Load and validate Weka model with proper error handling"""
    if not os.path.exists(model_path):
        IJ.log("Error: Model file not found: " + model_path)
        return None
        
    IJ.log("Loading Weka model from: " + model_path)
    try:
        from weka.core import SerializationHelper
        from java.io import FileInputStream
        
        # Load the model
        file_stream = FileInputStream(model_path)
        loaded_object = SerializationHelper.read(file_stream)
        file_stream.close()  # Always close file streams
        
        # Check if it's a classifier using duck typing (more reliable)
        if hasattr(loaded_object, 'classifyInstance'):
            IJ.log("Weka model loaded successfully: " + loaded_object.getClass().getName())
            return loaded_object
        else:
            IJ.log("Error: Loaded object is not a Weka Classifier")
            IJ.log("Object type: " + str(type(loaded_object)))
            return None
            
    except Exception as e:
        IJ.log("Error loading Weka model: " + str(e))
        IJ.log("Ensure the model file is valid and Weka is properly installed.")
        return None
    

def get_features_from_results_table(rt):
    """
    Extracts features from the ImageJ ResultsTable.
    Each row is a particle, columns are features.
    This function MUST be adapted if your ResultsTable contains non-feature columns (e.g., IDs, X/Y coordinates)
    or if features are not all numeric. The order of features returned must match how your Weka model was trained.
    """
    features_list = []
    num_rows = rt.getCounter()
    headings = rt.getHeadings() # These are the column names from the ResultsTable
    
    feature_headings = [] # Store the actual headings used as features

    # Example: Assuming all columns from results table are numeric features.
    # If not, you MUST filter/select appropriate columns here.
    # For instance, if your first column is 'Label' or 'ID', you'd skip it.
    for heading_idx, heading in enumerate(headings):
        # Add a check here if you need to exclude certain columns by name
        # if heading.lower() in ["id", "label", "x", "y"]:
        #     continue 
        feature_headings.append(heading)

    if not feature_headings:
        IJ.log("Error: No feature columns identified from ResultsTable headings. Please check 'get_features_from_results_table'.")
        return [], []

    for i in range(num_rows):
        row_features = []
        valid_row = True
        for fh_idx, fh in enumerate(feature_headings):
            try:
                value = rt.getValue(fh, i) # rt.getValue typically returns double
                row_features.append(value)
            except Exception as e: # More general exception for non-numeric or missing
                IJ.log("Warning: Could not get value for column '{}' at row {}. Skipping row. Error: {}".format(fh, i, e))
                valid_row = False
                break
        if valid_row and len(row_features) == len(feature_headings):
            features_list.append(row_features)
        elif valid_row and len(row_features) != len(feature_headings):
             IJ.log("Warning: Row {} had an unexpected number of features. Expected {}, got {}. Skipping row.".format(i, len(feature_headings), len(row_features)))


    # Return both the list of feature vectors and the headings corresponding to these features
    return features_list, feature_headings


# --- Main Script ---
def main():
    IJ.log("\\Clear")
    IJ.log("Starting script...")

    ensure_weka_in_classpath()
    test_weka_imports()
    if not validate_configuration():
        IJ.log("Script stopped due to configuration errors.")
        return
    

    # 1. Execute the macro
    IJ.log("Running macro: " + macro_path)
    try:
        # Ensure the macro runs on the current image or opens one.
        # If macro needs an argument (e.g. image title or path), adjust this call.
        IJ.runMacroFile(macro_path)
    except Exception as e:
        IJ.log("Error running macro: " + str(e))
        return

    # Get the results table
    rt = ResultsTable.getResultsTable()
    if rt is None or rt.getCounter() == 0:
        IJ.log("No results table found or table is empty after running the macro.")
        return
    IJ.log("Results table obtained with " + str(rt.getCounter()) + " entries.")

    # Extract features from the results table
    particle_features_list, feature_names_from_table = get_features_from_results_table(rt)
    if not particle_features_list:
        IJ.log("No features extracted from results table. Exiting.")
        return
    
    num_features_extracted = len(feature_names_from_table)
    IJ.log("Extracted {} features per particle with names: {}".format(num_features_extracted, ", ".join(feature_names_from_table)))


    # 2. Load Weka model and prepare data for classification

    # Load the Weka classifier model
    classifier = load_weka_model(model_path)
    if classifier is None:
        return # Exit if model loading failed

    # --- !!! IMPORTANT: USER MUST DEFINE Weka ATTRIBUTES HERE !!! ---
    # You MUST define the attributes exactly as they were when your Weka model was trained.
    # This includes:
    # 1. The number of attributes.
    # 2. The name of each attribute (often important for some models or for clarity).
    # 3. The type of each attribute (Numeric or Nominal).
    # 4. For Nominal attributes, the list of possible values.
    # 5. The Class Attribute, its name, and its labels (possible class values).
    # The order of attributes must also match the training data.

    attributes = ArrayList() # java.util.ArrayList to hold weka.core.Attribute objects

    # Example Scenario:
    # Your model was trained on 2 numeric features from the results table,
    # e.g., "Area" and "MeanIntensity", and predicts 3 classes "TypeA", "TypeB", "TypeC".

    # Define FEATURE attributes (example - REPLACE with your actual feature definitions)
    # This example assumes the features extracted by `get_features_from_results_table`
    # are all numeric and are in the correct order.
    if num_features_extracted == 0:
        IJ.log("Error: Zero features were extracted, cannot define attributes for Weka.")
        return
    
    attribute_names = ["%Area", "Solidity", "Convexity"]
    IJ.log("Attribute names given.")

    for i in range(num_features_extracted):
        # Using names from the table headings (spaces replaced with underscores)
        # If your Weka model was trained with specific attribute names, use those exact names.
        attr_name = attribute_names[i] # Sanitize name
        attributes.add(Attribute(attr_name)) # Default is NUMERIC attribute
        IJ.log("Defined NUMERIC attribute for Weka: " + attr_name)

    # If you have NOMINAL features, you need to define them like this:
    # nominal_feature_values = ArrayList()
    # nominal_feature_values.add("Value1")
    # nominal_feature_values.add("Value2")
    # attributes.add(Attribute("MyNominalFeatureName", nominal_feature_values))

    # Define CLASS attribute (must be the LAST attribute in the 'attributes' ArrayList)
    # These labels MUST EXACTLY match the class labels your Weka model was trained to predict.
    class_labels_list = ArrayList()
    class_labels_list.add("perfect")  # Replace with your actual first class label (e.g., "Healthy")
    class_labels_list.add("porous")  # Replace with your actual second class label (e.g., "Diseased_TypeA")
    class_labels_list.add("satellites")  # Replace with your actual third class label (e.g., "Diseased_TypeB")
    # Add more if your classifier predicts more than 3 classes.

    class_attribute_name = "Label" # Choose a name for your class attribute
    class_attribute = Attribute(class_attribute_name, class_labels_list)
    attributes.add(class_attribute)
    IJ.log("Defined CLASS attribute for Weka: " + class_attribute_name + " with labels: " + str(class_labels_list))
    # --- END OF USER-DEFINED ATTRIBUTES SECTION ---


    # Create a Weka Instances object to hold the data to be classified
    # The dataset name "ParticleDataToClassify" is arbitrary.
    # Initial capacity can be 0 or len(particle_features_list).
    instances_to_classify = Instances("ParticleDataToClassify", attributes, len(particle_features_list))
    # Set the class index. Since we added the class attribute last, its index is numAttributes() - 1.
    instances_to_classify.setClassIndex(instances_to_classify.numAttributes() - 1)

    # Populate the Instances object with data from particle_features_list
    for feature_vector in particle_features_list:
        # Ensure the feature_vector from results table matches the number of feature attributes defined (excluding class)
        if len(feature_vector) != (instances_to_classify.numAttributes() - 1):
            IJ.log("Error: Data row length ({}) does not match number of defined feature attributes ({}).".format(
                len(feature_vector), instances_to_classify.numAttributes() - 1))
            IJ.log("Feature vector: " + str(feature_vector))
            IJ.log("Please check 'get_features_from_results_table' and your attribute definitions.")
            # Decide whether to skip this instance or stop
            continue 

        # Create a new instance (DenseInstance is generally preferred)
        # The instance should have space for all attributes, including the class attribute.
        current_instance = DenseInstance(instances_to_classify.numAttributes())
        current_instance.setDataset(instances_to_classify) # Associate instance with the dataset structure

        # Set values for feature attributes
        for i, val in enumerate(feature_vector):
            # We assume features from table are numeric. If nominal, casting/handling is different.
            try:
                current_instance.setValue(i, float(val)) # 'i' is the index of the feature attribute
            except ValueError:
                IJ.log("Error: Could not convert value '{}' to float for feature '{}'. Instance skipped.".format(val, instances_to_classify.attribute(i).name()))
                current_instance = None # Mark instance as bad
                break
        
        if current_instance is None:
            continue

        # For test data, the class attribute is initially unknown.
        current_instance.setClassMissing()
        
        instances_to_classify.add(current_instance)

    if instances_to_classify.numInstances() == 0:
        IJ.log("No Weka instances were created for classification. Check feature extraction and attribute definitions.")
        return
    IJ.log("Created {} Weka instances for classification.".format(instances_to_classify.numInstances()))
    # For debugging, you can print the structure:
    # IJ.log("Weka Instances structure:\n" + instances_to_classify.toString())


    # Classify instances
    predictions = [] # List to store predicted class labels (strings)
    IJ.log("Starting classification of {} instances...".format(instances_to_classify.numInstances()))
    try:
        for i in range(instances_to_classify.numInstances()):
            instance_to_classify = instances_to_classify.instance(i)
            # classifyInstance returns the index of the predicted class label
            prediction_index = classifier.classifyInstance(instance_to_classify)
            # Get the actual string label of the predicted class
            predicted_class_label = instances_to_classify.classAttribute().value(int(prediction_index))
            predictions.append(predicted_class_label)
            # IJ.log("Instance {} predicted as: {}".format(i, predicted_class_label)) # Optional: log each prediction
    except Exception as e:
        IJ.log("Error during Weka classification: " + str(e))
        IJ.log("This can happen if the input data structure (Instances) is not compatible with the trained model,")
        IJ.log("or if a feature value is out of expected range for some models (e.g., NaN, Infinity).")
        return
    
    IJ.log("Classification complete. Number of predictions: " + str(len(predictions)))
    if len(predictions) != rt.getCounter():
         IJ.log("Warning: Number of predictions ({}) does not match number of rows in results table ({}). This may be due to skipped rows/instances.".format(len(predictions), rt.getCounter()))


    # 3. Color the respective particles
    # This assumes that the order of particles processed and ROIs in the ROI Manager correspond.
    # This is usually true if "Analyze Particles" was used with "Add to Manager" and results were not resorted.

    imp = WindowManager.getCurrentImage()
    if imp is None:
        IJ.log("No image open to draw ROIs on. Please ensure an image is active.")
        return

    roim = RoiManager.getInstance()
    if roim is None: # If ROI Manager is not open, try to create one
        roim = RoiManager(True) # True to make it not hidden
    
    if roim.getCount() == 0:
        IJ.log("ROI Manager is empty. Cannot color particles.")
        IJ.log("Make sure your macro '{}' adds ROIs to the ROI Manager.".format(macro_path))
        IJ.log("And that these ROIs correspond to the entries in the ResultsTable.")
        return

    # Check for mismatch in counts.
    # It's possible some particles were measured but not added to ROI manager, or some instances failed Weka prep.
    num_rois_in_manager = roim.getCount()
    num_predictions_made = len(predictions)
    
    if num_rois_in_manager != num_predictions_made:
        IJ.log("Warning: Number of ROIs in Manager ({}) does not match number of Weka predictions ({}).".format(
            num_rois_in_manager, num_predictions_made))
        IJ.log("Coloring will proceed up to the minimum of these two counts, assuming direct correspondence.")
        # This implies that the first N predictions map to the first N ROIs.
        # If your macro/analysis creates a different correspondence, this logic needs adjustment.

    num_items_to_color = min(num_rois_in_manager, num_predictions_made)
    if num_items_to_color == 0:
        IJ.log("Nothing to color.")
        return

    # Get the class labels as defined in your script (these were used to build 'instances_to_classify')
    defined_class_labels = [instances_to_classify.classAttribute().value(i) for i in range(instances_to_classify.classAttribute().numValues())]
    
    # Create a mapping from predicted class label string to color
    color_map = {}
    if len(defined_class_labels) >= 1:
        color_map[defined_class_labels[0]] = color_class1 # e.g., "Class1" -> RED
    if len(defined_class_labels) >= 2:
        color_map[defined_class_labels[1]] = color_class2 # e.g., "Class2" -> GREEN
    if len(defined_class_labels) >= 3:
        color_map[defined_class_labels[2]] = color_class3 # e.g., "Class3" -> BLUE
    # Add more if your model has more than 3 classes and you defined more colors
    
    IJ.log("Coloring {} ROIs...".format(num_items_to_color))
    
    # Ensure image has an overlay or create one
    overlay = imp.getOverlay()
    if overlay is None:
        from ij.gui import Overlay
        overlay = Overlay()
        imp.setOverlay(overlay)
        
    # It's good practice to deselect all ROIs in ROI Manager first
    # if you are not operating on a selection.
    roim.runCommand("Deselect All")

    for i in range(num_items_to_color):
        roi = roim.getRoi(i) # Get ROI by index from ROI Manager
        predicted_class_label = predictions[i] # Get corresponding prediction

        color_to_use = Color.GRAY # Default color if predicted class not in map
        if predicted_class_label in color_map:
            color_to_use = color_map[predicted_class_label]
        else:
            IJ.log("Warning: Predicted class '{}' for ROI #{} not found in color_map. Using default color (GRAY).".format(
                predicted_class_label, i))

        # Set properties of the ROI from the manager
        # These changes should reflect if the ROI manager is set to "Show All" on an overlay
        roi.setFillColor(color_to_use)  # Set fill color (for filled display)
        roi.setStrokeColor(color_to_use) # Set outline color
        
        # If ROIs are not automatically part of the image's main overlay via ROI Manager display modes,
        # you might need to explicitly add copies to the image's overlay.
        # However, modifying ROIs in the manager and then ensuring the manager's display is active usually works.
        # For safety, you could add a clone to the image's direct overlay:
        # cloned_roi = roi.clone()
        # cloned_roi.setFillColor(color_to_use)
        # cloned_roi.setStrokeColor(color_to_use)
        # overlay.add(cloned_roi)
        # If doing this, you might not want to modify roim.getRoi(i) directly, or clear the overlay first.
        # For simplicity, we assume modifying manager's ROIs is sufficient with "Show All".

    # Update the image display to show color changes and ensure ROIs are visible
    # roim.runCommand(imp, "Update") # May not be necessary, Show All might handle it
    roim.runCommand("Show All with labels") # Or "Show All" or "Labels"
    imp.updateAndDraw() # Refresh the image display

    IJ.log("Script finished. {} ROIs processed for coloring.".format(num_items_to_color))

# --- Run the script ---
if __name__ == '__main__':
    # In Fiji, Weka is typically available. For plain ImageJ, ensure weka.jar is in the classpath.
    # You might need to add it via:
    # Or place weka.jar in ImageJ/jre/lib/ext or ImageJ/plugins or ImageJ/jars.
    main()