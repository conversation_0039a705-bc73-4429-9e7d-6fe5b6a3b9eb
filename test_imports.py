#!/usr/bin/env python3
"""
Test script to check which machine learning modules are available
"""

import sys
print(f"Python version: {sys.version}")
print(f"Python executable: {sys.executable}")
print("-" * 50)

# Test core modules
modules_to_test = [
    'numpy',
    'pandas', 
    'matplotlib',
    'sklearn',
    'seaborn',
    'joblib',
    'openpyxl'
]

available_modules = []
missing_modules = []

for module in modules_to_test:
    try:
        __import__(module)
        available_modules.append(module)
        print(f"✅ {module} - Available")
    except ImportError as e:
        missing_modules.append(module)
        print(f"❌ {module} - Missing: {e}")

print("-" * 50)
print(f"Available modules: {len(available_modules)}/{len(modules_to_test)}")
print(f"Available: {', '.join(available_modules)}")
if missing_modules:
    print(f"Missing: {', '.join(missing_modules)}")

# Test specific sklearn imports if sklearn is available
if 'sklearn' in available_modules:
    print("\nTesting sklearn submodules:")
    sklearn_modules = [
        'sklearn.ensemble',
        'sklearn.model_selection', 
        'sklearn.metrics',
        'sklearn.preprocessing',
        'sklearn.linear_model',
        'sklearn.svm',
        'sklearn.tree'
    ]
    
    for module in sklearn_modules:
        try:
            __import__(module)
            print(f"✅ {module}")
        except ImportError as e:
            print(f"❌ {module}: {e}")

print("\nInstallation summary:")
if len(available_modules) == len(modules_to_test):
    print("🎉 All modules are available! You're ready for machine learning.")
else:
    print(f"⚠️  {len(missing_modules)} modules need to be installed.")
    print("Run the following commands to install missing modules:")
    for module in missing_modules:
        print(f"  pip install {module}")
