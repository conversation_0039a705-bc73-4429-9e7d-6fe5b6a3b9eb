requires("1.31g");
run("8-bit");
setAutoThreshold("Minimum");
run("Make Binary");
run("Invert");
run("Set Scale...", "distance=214 known=50 unit=µm global");
run("Set Measurements...", "area perimeter area_fraction fit shape feret's");
run("Analyze Particles...", "size=5-infinity minimum=50 show=Overlay add display exclude include clear record");

n = nResults;
	
deleted_entries = 0;

for (i=n-1; i>=0; i--) {
    if ((getResult('XStart', i)>2260) && (getResult('YStart', i)>1565))	{
		roiManager("select", i);
		roiManager("Delete");
		deleted_entries++;
	}
}
n = n-deleted_entries;

conv_perim = newArray(n);


// Convexity measurements
for (i=0; i<n; i++) {
	xstart = getResult("XStart", i);
	ystart = getResult("YStart", i);
    doWand(xstart, ystart);
    run("Convex Hull");
    run("Measure");
    conv_perim[i] = getResult('Perim.', n+i);
}


// Define group names
groups = newArray("Round_NonPorous", "Round_Porous", "Satellite_NonPorous", 
                  "Satellite_Porous", "Splattered_NonPorous", "Splattered_Porous");

// Initialize arrays to store classifications
nParticles = roiManager("count");
classifications = newArray(nParticles);
classified = newArray(nParticles);

// Fill arrays with default values
for (i = 0; i < nParticles; i++) {
    classifications[i] = "Unclassified";
    classified[i] = false;
}

// Ask user if they want to manually classify particles
Dialog.create("Manual Classification");
Dialog.addMessage("Do you want to manually classify the " + nParticles + " detected particles?");
Dialog.addCheckbox("Enable manual classification", true);
Dialog.show();

if (Dialog.getCheckbox()) {
    manualClassification(groups, classifications, classified);
    exportToExcel(groups, classifications);
}

// Function for manual classification
function manualClassification(groups, classifications, classified) {
    print("\\Clear");
    print("=== MANUAL CLASSIFICATION MODE ===");
    print("Instructions:");
    print("1. Click on a particle in the image or select it in ROI Manager");
    print("2. Press OK in the instruction dialog");
    print("3. Choose classification in the next dialog");
    print("4. Repeat for all particles");
    print("");
    
    // Make sure ROI Manager is visible
    if (!isOpen("ROI Manager")) {
        run("ROI Manager...");
    }
    
    // Classification loop
    currentParticle = 0;
    finished = false;
    
    while (!finished) {
        // Show progress
        classified_count = 0;
        for (i = 0; i < nParticles; i++) {
            if (classified[i]) classified_count++;
        }
        
        // Non-blocking particle selection window
        getParticleSelection(classified_count, nParticles, currentParticle);

        // Get currently selected ROI
        selectedIndex = roiManager("index");
        if (selectedIndex >= 0) {
            currentParticle = selectedIndex;
        }

        // Classification window with finalize option
        classificationResult = getClassificationAction(currentParticle, nParticles, groups);

        action = classificationResult[0];
        selectedGroup = classificationResult[1];
        shouldFinalize = classificationResult[2];

        // Handle classification first if requested
        if (action == "classify") {
            // Validate and classify
            if (currentParticle >= 0 && currentParticle < nParticles) {
                classifications[currentParticle] = selectedGroup;
                classified[currentParticle] = true;

                // Highlight the classified particle
                roiManager("select", currentParticle);
                roiManager("Set Color", getGroupColor(selectedGroup));

                print("Particle " + (currentParticle + 1) + " classified as: " + selectedGroup);

                // Move to next unclassified particle
                currentParticle = getNextUnclassified(classified);
            }
        }

        // Handle finalization after classification (if both were selected)
        if (shouldFinalize == "true") {
            finished = true;
            break;
        }

        // If action is "back", just continue the loop without classifying
    }
    
    print("Classification completed!");
    return classifications;
}

// Helper function to get next unclassified particle
function getNextUnclassified(classified) {
    for (i = 0; i < classified.length; i++) {
        if (!classified[i]) return i;
    }
    return 0; // All classified, return to first
}

// Non-blocking particle selection window (simplified)
function getParticleSelection(classified_count, nParticles, currentParticle) {
    // Use waitForUser for truly non-blocking interaction
    waitForUser("Particle Selection (" + (classified_count + 1) + "/" + nParticles + ")",
               "Progress: " + classified_count + "/" + nParticles + " particles classified\n \n" +
               "Select a particle in the image or ROI Manager\n" +
               "Currently suggesting particle: " + (currentParticle + 1) + "\n \n" +
               "Click OK to proceed to classification\n" +
               "You can freely interact with the image while this dialog is open!");
}

// Classification window with classify, back, and finalize options
function getClassificationAction(currentParticle, nParticles, groups) {
    Dialog.create("Classify Particle " + (currentParticle + 1));
    Dialog.addMessage("Classifying particle: " + (currentParticle + 1) + "/" + nParticles);
    Dialog.addChoice("Classification:", groups, groups[0]);
    Dialog.addCheckbox("Classify (save classification)", true); // Default selected
    Dialog.addCheckbox("Back (return to selection)", false);
    Dialog.addCheckbox("Finalize classification process", false);
    Dialog.show();

    selectedGroup = Dialog.getChoice();
    classify = Dialog.getCheckbox();
    back = Dialog.getCheckbox();
    finalize = Dialog.getCheckbox();

    // Determine action and finalization
    if (back) {
        return newArray("back", "", "false");
    } else if (classify && finalize) {
        // Both selected: classify first, then finalize
        return newArray("classify", selectedGroup, "true");
    } else if (classify) {
        // Only classify
        return newArray("classify", selectedGroup, "false");
    } else if (finalize) {
        // Only finalize (without classifying current particle)
        return newArray("finalize", "", "true");
    } else {
        // Nothing selected, treat as back
        return newArray("back", "", "false");
    }
}

// Helper function to assign colors to groups
function getGroupColor(group) {
    if (indexOf(group, "Round") >= 0) {
        if (indexOf(group, "Porous") >= 0) return "blue";
        else return "cyan";
    } else if (indexOf(group, "Satellite") >= 0) {
        if (indexOf(group, "Porous") >= 0) return "red";
        else return "orange";
    } else if (indexOf(group, "Splattered") >= 0) {
        if (indexOf(group, "Porous") >= 0) return "magenta";
        else return "yellow";
    }
    return "white";
}

// Function to export results to Excel
function exportToExcel(groups, classifications) {
    print("\\Clear");
    print("=== EXPORTING TO EXCEL ===");

    // Get output directory
    outputDir = getDirectory("Choose directory for Excel export");
    if (outputDir == "") return;

    // Get base name without extension
    fullTitle = getTitle();
    dotIndex = lastIndexOf(fullTitle, ".");
    if (dotIndex > 0) {
        baseName = substring(fullTitle, 0, dotIndex);
    } else {
        baseName = fullTitle;
    }

    // Create single Excel file path
    excelFile = outputDir + baseName + ".xlsx";

    // Export each group as separate worksheet in the same Excel file
    for (g = 0; g < groups.length; g++) {
        groupName = groups[g];

        // Create results table for this group
        groupIndices = newArray(0);
        for (i = 0; i < classifications.length; i++) {
            if (classifications[i] == groupName) {
                groupIndices = Array.concat(groupIndices, i);
            }
        }

        if (groupIndices.length > 0) {
            // Prepare results table for this group
            prepareGroupResults(groupIndices, groupName);

            // Export to Excel worksheet
            run("Read and Write Excel", "file=[" + excelFile + "] sheet=[" + groupName + "] no_count_column stack_results");
            print("Exported " + groupIndices.length + " particles to worksheet: " + groupName);
        } else {
            print("No particles in group: " + groupName);
        }
    }

    // Also export summary as a separate worksheet
    exportSummaryToExcel(excelFile, groups, classifications);

    print("Export completed!");
    showMessage("Export completed!\nFile saved as: " + excelFile);
}

// Function to prepare results table for a specific group
function prepareGroupResults(indices, groupName) {
    // Clear results table
    run("Clear Results");

    // Copy relevant rows from original results
    for (i = 0; i < indices.length; i++) {
        idx = indices[i];

        // Select the ROI to get fresh measurements
        roiManager("select", idx);
        run("Measure");
        setResult("Convexity", i, conv_perim[i]/getResult("Perim.", i));

        // Add group information to the result
        setResult("Particle_ID", i, idx + 1);  // Use 1-based numbering for export
        setResult("Group", i, groupName);
    }

    updateResults();
}

// Function to export classification summary to Excel
function exportSummaryToExcel(excelFile, groups, classifications) {
    run("Clear Results");

    for (g = 0; g < groups.length; g++) {
        count = 0;
        for (i = 0; i < classifications.length; i++) {
            if (classifications[i] == groups[g]) count++;
        }
        setResult("Group", g, groups[g]);
        setResult("Count", g, count);
        setResult("Percentage", g, (count * 100.0 / classifications.length));
    }

    updateResults();

    // Export summary to Excel worksheet
    run("Read and Write Excel", "file=[" + excelFile + "] sheet=[Summary] no_count_column stack_results");
    print("Exported summary to worksheet: Summary");
}