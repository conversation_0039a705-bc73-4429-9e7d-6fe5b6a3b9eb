@echo off
set PARTICLE_ANALYSIS_MACRO="C:\Users\<USER>\Fiji.app\macros\extract_ASC_for_batch_classification.ijm"
set PARTICLE_ANALYSIS_MODEL="C:\Users\<USER>\Fiji.app\models\perceptron_3_classes_ASC.model"
set PARTICLE_ANALYSIS_INPUT_DIR="C:\Studium\Johann\Partikeluntersuchungen\63um\Test"
set PARTICLE_ANALYSIS_FILE_TYPES=tif
set PARTICLE_ANALYSIS_CLEANUP=true

"C:\Users\<USER>\Fiji.app\ImageJ-win64.exe" --run "C:\Users\<USER>\Fiji.app\scripts\Batch-processing-Compact.py"
pause