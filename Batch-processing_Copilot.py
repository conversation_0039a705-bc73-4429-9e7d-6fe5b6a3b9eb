# ============================================================================
# Weka Particle Classification Script for ImageJ
# ============================================================================
# 
# This script automates the process of:
# 1. Running a macro on microscopy images
# 2. Extracting particle features
# 3. Classifying particles using a pre-trained Weka model
# 4. Visualizing results by coloring particles
# 5. Generating statistical reports
#
# Key Features:
# - Batch processing of multiple images
# - Automatic feature validation and ordering
# - Model compatibility checks
# - UTF-8 safe file handling
# - Robust error handling
# - Automatic cleanup
# 
# Requirements:
# - ImageJ/Fiji
# - Weka 3.8.6 or later
# - Pre-trained Weka model
# - Image processing macro
# ============================================================================

# --- Standard Library Imports ---
import sys
import os

# --- ImageJ/Fiji Imports ---
from ij import IJ, WindowManager
from ij.measure import ResultsTable
from ij.plugin.frame import RoiManager
from ij.gui import Overlay

# --- Java Imports ---
from java.io import FileInputStream, File, FileWriter, BufferedWriter
from java.awt import Color
from java.net import URL
from java.lang import ClassLoader, Class, System
from java.util import ArrayList

# --- Configuration Constants ---
# Paths and file patterns for batch processing
macro_path = "C:/Users/<USER>/Fiji.app/macros/extract_ASC_for_batch_classification.ijm"
model_path = "C:/Studium/Johann/Partikeluntersuchungen/63um/perceptron_3_classes_ASC.model"
USER_WEKA_JAR_PATH = "C:/Program Files/Weka-3-8-6/weka.jar"
import_dir = "C:/Studium/Johann/Partikeluntersuchungen/63um/Fotos"
file_types = "tif"
filters = "63"
CLEANUP_ON_EXIT = True

# Model configuration
REQUIRED_FEATURES = ["%Area", "Solidity", "Convexity"]  # Update based on your model
DEFAULT_CLASS_LABELS = ["perfect", "porous", "satellites"]

# Visualization colors (RGBA format)
color_class1 = Color(255, 0, 0, 100)    # Red for first class
color_class2 = Color(0, 255, 0, 100)    # Green for second class
color_class3 = Color(0, 0, 255, 100)    # Blue for third class

# Global Weka class references
WekaAttribute = None
WekaInstances = None
WekaDenseInstance = None
WekaSerializationHelper = None

# --- Helper Functions ---

def ensure_weka_in_classpath():
    """
    Checks if Weka classes can be imported and attempts to add Weka JAR to classpath if needed.
    """
    try:
        # Try to find a basic Weka class
        Class.forName("weka.core.Attribute")
        IJ.log("Weka libraries successfully found in ImageJ classpath.")
        return True
    except Exception as e:
        IJ.log("Weka libraries not found in classpath: " + str(e))
        
        # Check if we have a path to try
        if not USER_WEKA_JAR_PATH:
            IJ.log("No path for weka.jar configured. Please set USER_WEKA_JAR_PATH.")
            return False

        if not os.path.exists(USER_WEKA_JAR_PATH):
            IJ.log("Weka JAR not found at: " + USER_WEKA_JAR_PATH)
            return False

        IJ.log("Attempting to add Weka to classpath from: " + USER_WEKA_JAR_PATH)
        try:
            # Add JAR to classpath using reflection
            jar_file = File(USER_WEKA_JAR_PATH)
            jar_url = jar_file.toURI().toURL()
            sys_loader = ClassLoader.getSystemClassLoader()
            method = sys_loader.getClass().getDeclaredMethod("addURL", [URL])
            method.setAccessible(True)
            method.invoke(sys_loader, [jar_url])
            IJ.log("Weka jar added to classpath.")
            
            # Verify successful loading
            Class.forName("weka.core.Attribute")
            IJ.log("Weka classes now accessible after adding JAR.")
            return True
            
        except Exception as e2:
            IJ.log("✗ Failed to add Weka to classpath: " + str(e2))
            return False

def import_weka_classes():
    """
    Import Weka classes and store them in global variables.
    This must be called after ensuring Weka is in classpath.
    """
    global WekaAttribute, WekaInstances, WekaDenseInstance, WekaSerializationHelper
    
    try:
        # Import core Weka classes
        from weka.core import Attribute as WekaAttribute
        from weka.core import Instances as WekaInstances  
        from weka.core import DenseInstance as WekaDenseInstance
        from weka.core import SerializationHelper as WekaSerializationHelper
        
        IJ.log("Successfully imported Weka classes:")
        IJ.log("  - Attribute: " + str(WekaAttribute))
        IJ.log("  - Instances: " + str(WekaInstances))
        IJ.log("  - DenseInstance: " + str(WekaDenseInstance))
        IJ.log("  - SerializationHelper: " + str(WekaSerializationHelper))
        return True
        
    except ImportError as e:
        IJ.log("Failed to import Weka classes: " + str(e))
        IJ.log("Available Weka classes might be limited. Check your Weka installation.")
        return False
    except Exception as e:
        IJ.log("Unexpected error importing Weka classes: " + str(e))
        return False

def test_weka_functionality():
    """Test basic Weka functionality to ensure everything works"""
    try:
        # Test creating a simple attribute
        test_attr = WekaAttribute("test_numeric")
        IJ.log("Test: Created numeric attribute successfully")
        
        # Test creating nominal attribute
        nominal_values = ArrayList()
        nominal_values.add("class1")
        nominal_values.add("class2")
        test_nominal = WekaAttribute("test_nominal", nominal_values)
        IJ.log("Test: Created nominal attribute successfully")
        
        # Test creating instances structure
        attrs = ArrayList()
        attrs.add(test_attr)
        attrs.add(test_nominal)
        test_instances = WekaInstances("test", attrs, 0)
        test_instances.setClassIndex(1)
        IJ.log("Test: Created Instances structure successfully")
        
        return True
        
    except Exception as e:
        IJ.log("Weka functionality test failed: " + str(e))
        return False

def validate_configuration():
    """Validate all configuration settings before running main script"""
    errors = []
    warnings = []
    
    # Check macro file
    if not os.path.exists(macro_path):
        errors.append("Macro file not found: " + macro_path)
    
    # Check model file
    if not os.path.exists(model_path):
        errors.append("Model file not found: " + model_path)
    
    # Check Weka JAR if specified
    if USER_WEKA_JAR_PATH and not os.path.exists(USER_WEKA_JAR_PATH):
        warnings.append("Weka JAR not found at: " + USER_WEKA_JAR_PATH)
    
    # Check if an image is open
    if WindowManager.getCurrentImage() is None:
        warnings.append("No image is currently open. Macro may fail if it expects an active image.")
    
    # Report results
    if errors:
        IJ.log("CONFIGURATION ERRORS:")
        for error in errors:
            IJ.log("  - " + error)
        return False
    
    if warnings:
        IJ.log("CONFIGURATION WARNINGS:")
        for warning in warnings:
            IJ.log("  - " + warning)
    
    IJ.log("Configuration validation passed.")
    return True

def load_weka_model(model_path):
    """Load and validate Weka model with proper error handling"""
    if not os.path.exists(model_path):
        IJ.log("Error: Model file not found: " + model_path)
        return None
        
    IJ.log("Loading Weka model from: " + model_path)
    try:
        # Load the model using the imported SerializationHelper
        file_stream = FileInputStream(model_path)
        loaded_object = WekaSerializationHelper.read(file_stream)
        file_stream.close()
        
        # Check if it's a classifier
        if hasattr(loaded_object, 'classifyInstance'):
            IJ.log("Weka model loaded successfully: " + loaded_object.getClass().getName())
            return loaded_object
        else:
            IJ.log("Error: Loaded object is not a Weka Classifier")
            IJ.log("Object type: " + str(type(loaded_object)))
            return None
            
    except Exception as e:
        IJ.log("Error loading Weka model: " + str(e))
        return None

def analyze_weka_model(classifier):
    """
    Analyze the loaded Weka classifier to extract information about
    required features and class labels.
    
    Returns:
        tuple: (feature_names_list, class_labels_list, analysis_success)
    """
    IJ.log("Analyzing Weka model structure...")
    
    try:
        # Try to get capabilities - this tells us about input requirements
        if hasattr(classifier, 'getCapabilities'):
            capabilities = classifier.getCapabilities()
            IJ.log("Model capabilities obtained")
        else:
            IJ.log("Model doesn't expose capabilities")
            capabilities = None
        
        # Try to get training header/instances structure
        training_header = None
        feature_names = []
        class_labels = []
        
        # Method 1: Check if model stores training header
        if hasattr(classifier, 'getHeader'):
            try:
                training_header = classifier.getHeader()
                IJ.log("Found training header in model")
            except:
                pass
        
        # Method 2: Some models store it as m_Header
        if training_header is None and hasattr(classifier, 'm_Header'):
            try:
                training_header = classifier.m_Header
                IJ.log("Found m_Header in model")
            except:
                pass
        
        # Method 3: Some FilteredClassifiers wrap the header
        if training_header is None and hasattr(classifier, 'getClassifier'):
            try:
                base_classifier = classifier.getClassifier()
                if hasattr(base_classifier, 'getHeader'):
                    training_header = base_classifier.getHeader()
                    IJ.log("Found header in wrapped classifier")
                elif hasattr(base_classifier, 'm_Header'):
                    training_header = base_classifier.m_Header
                    IJ.log("Found m_Header in wrapped classifier")
            except:
                pass
        
        # Extract information from training header if available
        if training_header is not None:
            IJ.log("Extracting feature and class information from training header...")
            
            # Get feature names (all attributes except class)
            num_attributes = training_header.numAttributes()
            class_index = training_header.classIndex()
            
            IJ.log("Model expects {} total attributes (including class)".format(num_attributes))
            IJ.log("Class attribute is at index: {}".format(class_index))
            
            # Extract feature attribute names
            for i in range(num_attributes):
                attr = training_header.attribute(i)
                if i != class_index:  # Skip class attribute
                    feature_names.append(str(attr.name()))
            
            # Extract class labels
            if class_index >= 0:
                class_attr = training_header.classAttribute()
                if class_attr.isNominal():
                    for i in range(class_attr.numValues()):
                        class_labels.append(str(class_attr.value(i)))
                else:
                    IJ.log("Warning: Class attribute is not nominal (it's numeric)")
            
            # Log findings
            IJ.log("=== MODEL ANALYSIS RESULTS ===")
            IJ.log("Required features ({} total):".format(len(feature_names)))
            for i, name in enumerate(feature_names):
                IJ.log("  {}. {}".format(i+1, name))
            
            IJ.log("Class labels ({} total):".format(len(class_labels)))
            for i, label in enumerate(class_labels):
                IJ.log("  {}. {}".format(i+1, label))
            IJ.log("===============================")
            
            return feature_names, class_labels, True
        
        else:
            IJ.log("Could not extract training header from model")
            
            # Fallback: Try to analyze model string representation
            model_string = str(classifier)
            IJ.log("Attempting to parse model string representation...")
            
            # Some models include attribute info in their string representation
            if "Attribute" in model_string or "attributes" in model_string:
                IJ.log("Model string contains attribute information:")
                # Print relevant parts of the model string
                lines = model_string.split('\n')
                for line in lines[:20]:  # First 20 lines usually contain structure info
                    if any(keyword in line.lower() for keyword in ['attribute', 'class', 'feature']):
                        IJ.log("  " + line.strip())
            
            IJ.log("Manual configuration of REQUIRED_FEATURES and class labels is needed")
            return [], [], False
    
    except Exception as e:
        IJ.log("Error analyzing model: " + str(e))
        IJ.log("Manual configuration of REQUIRED_FEATURES and class labels is needed")
        return [], [], False

def auto_configure_from_model(feature_names, class_labels):
    """
    Update global configuration based on model analysis results.
    """
    global REQUIRED_FEATURES
    
    if feature_names:
        IJ.log("Auto-configuring REQUIRED_FEATURES from model...")
        REQUIRED_FEATURES = feature_names
        IJ.log("Updated REQUIRED_FEATURES: " + str(REQUIRED_FEATURES))
        return True
    else:
        IJ.log("Could not auto-configure features. Using manual REQUIRED_FEATURES configuration.")
        return False

def get_features_from_results_table(rt):
    """
    Extracts features from the ImageJ ResultsTable.
    """
    features_list = []
    num_rows = rt.getCounter()
    headings = rt.getHeadings()
    
    feature_headings = []
    
    # Filter headings - exclude common non-feature columns
    excluded_columns = ["label", "id", "x", "y", "bx", "by", "width", "height"]
    
    for heading in headings:
        if heading.lower() not in excluded_columns:
            feature_headings.append(heading)

    if not feature_headings:
        IJ.log("Error: No feature columns identified from ResultsTable headings.")
        IJ.log("Available headings: " + str(list(headings)))
        return [], []

    IJ.log("Using feature columns: " + str(feature_headings))

    for i in range(num_rows):
        row_features = []
        valid_row = True
        
        for heading in feature_headings:
            try:
                value = rt.getValue(heading, i)
                # Check for invalid values
                if str(value).lower() in ['nan', 'infinity', '-infinity']:
                    IJ.log("Warning: Invalid value '{}' in column '{}' at row {}. Skipping row.".format(value, heading, i))
                    valid_row = False
                    break
                row_features.append(float(value))
            except Exception as e:
                IJ.log("Warning: Could not get value for column '{}' at row {}. Error: {}".format(heading, i, e))
                valid_row = False
                break
                
        if valid_row and len(row_features) == len(feature_headings):
            features_list.append(row_features)

    IJ.log("Extracted {} valid feature rows from {} total rows".format(len(features_list), num_rows))
    return features_list, feature_headings

def create_weka_instances(particle_features_list, feature_names, class_labels=None):
    """Create Weka Instances object with proper attribute definitions"""
    
    # Create attributes list
    attributes = ArrayList()
    
    # Use the provided feature names (should already be ordered correctly)
    for name in feature_names:
        # Create numeric attribute
        attr = WekaAttribute(name.replace(" ", "_"))  # Replace spaces with underscores
        attributes.add(attr)
        IJ.log("Added numeric attribute: " + name)

    # Define class attribute (must be last)
    if class_labels is None or len(class_labels) == 0:
        # Use default class labels if none provided
        class_labels_list = ArrayList()
        for label in DEFAULT_CLASS_LABELS:
            class_labels_list.add(label)
        IJ.log("Using default class labels")
    else:
        # Use class labels from model analysis
        class_labels_list = ArrayList()
        for label in class_labels:
            class_labels_list.add(label)
        IJ.log("Using class labels from model: " + str(class_labels))
    
    class_attr = WekaAttribute("Label", class_labels_list)
    attributes.add(class_attr)

    # Create Instances object
    instances = WekaInstances("ParticleData", attributes, len(particle_features_list))
    instances.setClassIndex(instances.numAttributes() - 1)

    # Add data instances
    for feature_vector in particle_features_list:
        if len(feature_vector) != (instances.numAttributes() - 1):
            IJ.log("Skipping row: feature count mismatch")
            continue
            
        # Create instance
        instance = WekaDenseInstance(instances.numAttributes())
        instance.setDataset(instances)
        
        # Set feature values
        for i, value in enumerate(feature_vector):
            try:
                instance.setValue(i, float(value))
            except Exception as e:
                IJ.log("Error setting value: " + str(e))
                instance = None
                break
                
        if instance is not None:
            instance.setClassMissing()  # Class is unknown for prediction
            instances.add(instance)

    IJ.log("Created {} Weka instances for classification".format(instances.numInstances()))
    return instances
    
def validate_and_order_features(particle_features_list, feature_names, required_features):
    """
    Validates that all required features are present, reorders them to match model expectations,
    and removes any unnecessary features.
    
    Args:
        particle_features_list: List of feature vectors (each vector is a list of values)
        feature_names: List of feature names from the results table
        required_features: List of required feature names in the correct order for the model
    
    Returns:
        tuple: (ordered_features_list, ordered_feature_names, success_flag)
    """
    IJ.log("Validating and ordering features for model compatibility...")
    
    # Check if all required features are present
    missing_features = []
    for required_feature in required_features:
        if required_feature not in feature_names:
            missing_features.append(required_feature)
    
    if missing_features:
        IJ.log("ERROR: Missing required features for the model:")
        for missing in missing_features:
            IJ.log("  - " + missing)
        IJ.log("Available features: " + str(feature_names))
        IJ.log("Required features: " + str(required_features))
        return [], [], False
    
    # Create mapping from feature name to index in original data
    feature_index_map = {}
    for i, name in enumerate(feature_names):
        feature_index_map[name] = i
    
    # Get indices of required features in the correct order
    required_indices = []
    for required_feature in required_features:
        required_indices.append(feature_index_map[required_feature])
    
    IJ.log("Feature mapping:")
    for i, required_feature in enumerate(required_features):
        original_index = required_indices[i]
        IJ.log("  {} (position {}) -> {} (position {})".format(
            feature_names[original_index], original_index, required_feature, i))
    
    # Reorder and filter features for each particle
    ordered_features_list = []
    
    for particle_features in particle_features_list:
        if len(particle_features) != len(feature_names):
            IJ.log("Warning: Particle has {} features but expected {}. Skipping.".format(
                len(particle_features), len(feature_names)))
            continue
            
        # Extract only the required features in the correct order
        ordered_particle_features = []
        for required_index in required_indices:
            ordered_particle_features.append(particle_features[required_index])
        
        ordered_features_list.append(ordered_particle_features)
    
    # Log summary
    original_feature_count = len(feature_names)
    required_feature_count = len(required_features)
    original_particle_count = len(particle_features_list)
    final_particle_count = len(ordered_features_list)
    
    IJ.log("Feature validation and ordering complete:")
    IJ.log("  - Reduced features from {} to {} (removed {} unnecessary features)".format(
        original_feature_count, required_feature_count, 
        original_feature_count - required_feature_count))
    IJ.log("  - Processed {} out of {} particles successfully".format(
        final_particle_count, original_particle_count))
    
    if original_feature_count > required_feature_count:
        removed_features = [f for f in feature_names if f not in required_features]
        IJ.log("  - Removed features: " + str(removed_features))
    
    return ordered_features_list, required_features, True
    
def color_particles(predictions, color_map):
    """Color particles based on predictions"""
    imp = WindowManager.getCurrentImage()
    if imp is None:
        IJ.log("No image open to draw ROIs on.")
        return

    roim = RoiManager.getInstance()
    if roim is None:
        roim = RoiManager(True)
    
    if roim.getCount() == 0:
        IJ.log("ROI Manager is empty. Cannot color particles.")
        return

    num_rois = roim.getCount()
    num_predictions = len(predictions)
    num_to_color = min(num_rois, num_predictions)
    
    if num_to_color == 0:
        IJ.log("Nothing to color.")
        return

    IJ.log("Coloring {} ROIs based on predictions...".format(num_to_color))
    
    # Ensure image has overlay
    overlay = imp.getOverlay()
    if overlay is None:
        overlay = Overlay()
        imp.setOverlay(overlay)

    roim.runCommand("Deselect All")

    for i in range(num_to_color):
        roi = roim.getRoi(i)
        predicted_class = predictions[i]
        
        color_to_use = color_map.get(predicted_class, Color.GRAY)
        
        roi.setFillColor(color_to_use)
        roi.setStrokeColor(color_to_use)

    roim.runCommand("Show All with labels")
    imp.updateAndDraw()
    
    IJ.log("Finished coloring {} ROIs".format(num_to_color))

def batch_open_images(path, file_type=None, name_filter=None):
    '''Open all files in the given folder.
    :param path: The path from were to open the images. String and java.io.File are allowed.
    :param file_type: Only accept files with the given extension (default: None).
    :param name_filter: Only accept files that contain the given string (default: None).
    :param recursive: Process directories recursively (default: False).
    '''
    # Converting a File object to a string.
    if isinstance(path, File):
        path = path.getAbsolutePath()
 
    def check_type(string):
        '''This function is used to check the file type.
        It is possible to use a single string or a list/tuple of strings as filter.
        This function can access the variables of the surrounding function.
        :param string: The filename to perform the check on.
        '''
        if file_type:
            # The first branch is used if file_type is a list or a tuple.
            if isinstance(file_type, (list, tuple)):
                for file_type_ in file_type:
                    if string.endswith(file_type_):
                        # Exit the function with True.
                        return True
                    else:
                        # Next iteration of the for loop.
                        continue
            # The second branch is used if file_type is a string.
            elif isinstance(file_type, string):
                if string.endswith(file_type):
                    return True
                else:
                    return False
            return False
        # Accept all files if file_type is None.
        else:
            return True
        

    def check_filter(string):
        '''This function is used to check for a given filter.
        It is possible to use a single string or a list/tuple of strings as filter.
        This function can access the variables of the surrounding function.
        :param string: The filename to perform the filtering on.
        '''
        if name_filter:
            # The first branch is used if name_filter is a list or a tuple.
            if isinstance(name_filter, (list, tuple)):
                for name_filter_ in name_filter:
                    if name_filter_ in string:
                        # Exit the function with True.
                        return True
                    else:
                        # Next iteration of the for loop.
                        continue
            # The second branch is used if name_filter is a string.
            elif isinstance(name_filter, string):
                if name_filter in string:
                    return True
                else:
                    return False
            return False
        else:
        # Accept all files if name_filter is None.
            return True

    # We collect all files to open in a list.
    path_to_images = []
    # Replacing some abbreviations (e.g. $HOME on Linux).
    path = os.path.expanduser(path)
    path = os.path.expandvars(path)
    # If we don't want a recursive search, we can use os.listdir().
    for file_name in os.listdir(path):
        full_path = os.path.join(path, file_name)
        if os.path.isfile(full_path):
            if check_type(file_name):
                if check_filter(file_name):
                    path_to_images.append(full_path)
    
    return path_to_images

def split_string(input_string):
    '''Split a string to a list and strip it
    :param input_string: A string that contains semicolons as separators.
    '''
    string_splitted = input_string.split(';')
    # Remove whitespace at the beginning and end of each string
    strings_striped = [string.strip() for string in string_splitted]
    return strings_striped


# --- Utility Functions ---
def find_image_files(directory, file_types=None, name_filters=None):
    """
    Find all matching image files in a directory.
    
    Args:
        directory (str): Directory path to search
        file_types (str/list): File extensions to include
        name_filters (str/list): Strings that must be in filename
        
    Returns:
        list: List of matching file paths
    """
    if isinstance(directory, File):
        directory = directory.getAbsolutePath()
        
    # Convert string inputs to lists
    if isinstance(file_types, str):
        file_types = [file_types]
    if isinstance(name_filters, str):
        name_filters = [name_filters]
        
    matching_files = []
    dir_file = File(directory)
    
    if dir_file.isDirectory():
        for entry in dir_file.listFiles():
            if not entry.isFile():
                continue
                
            filename = entry.getName()
            
            # Check file extension
            matches_type = False
            if not file_types:
                matches_type = True
            else:
                for ext in file_types:
                    if filename.lower().endswith(ext.lower()):
                        matches_type = True
                        break
                        
            # Check filename filters
            matches_filter = False
            if not name_filters:
                matches_filter = True
            else:
                for filter_str in name_filters:
                    if filter_str.lower() in filename.lower():
                        matches_filter = True
                        break
                        
            if matches_type and matches_filter:
                matching_files.append(entry.getAbsolutePath())
                
    if matching_files:
        IJ.log("Found {} matching files in {}".format(len(matching_files), directory))
    return matching_files

def write_results_csv(output_path, all_predictions, prediction_stats, class_labels):
    """
    Write classification results to CSV with proper UTF-8 handling.
    
    Args:
        output_path (str): Path to output CSV file
        all_predictions (list): List of (image_name, predictions) tuples
        prediction_stats (dict): Overall prediction statistics
        class_labels (list): List of class labels
    """
    writer = None
    try:
        writer = BufferedWriter(FileWriter(File(output_path)))
        
        # Write header
        writer.write('Image,' + ','.join(class_labels) + '\n')
        
        # Write per-image results
        for img_name, img_stats in all_predictions:
            row = [img_name] + [str(img_stats.get(label, 0)) for label in class_labels]
            writer.write(','.join(row) + '\n')
            
        # Write totals
        total_row = ['TOTAL'] + [str(prediction_stats.get(label, 0)) for label in class_labels]
        writer.write(','.join(total_row) + '\n')
        
        # Write percentages
        total_particles = sum(prediction_stats.values())
        if total_particles > 0:            
            pct_row = ['PERCENTAGE'] + [
                "{:.2f}%".format((prediction_stats.get(label, 0) / float(total_particles) * 100))
                for label in class_labels]
            writer.write(','.join(pct_row) + '\n')
            
    except Exception as e:
        IJ.log("Error writing results: {}".format(str(e)))
    finally:
        if writer:
            writer.close()

def cleanup_workspace():
    """
    Clean up ImageJ workspace by closing windows and resetting tables.
    """
    # Close all images
    image_ids = WindowManager.getIDList()
    if image_ids:
        for id in image_ids:
            imp = WindowManager.getImage(id)
            if imp:
                imp.changes = False
                imp.close()
                
    # Reset and close Results
    rt = ResultsTable.getResultsTable()
    if rt:
        rt.reset()
        IJ.run("Close")
        
    # Reset and close ROI Manager
    roi_manager = RoiManager.getInstance()
    if roi_manager:
        roi_manager.reset()
        roi_manager.close()
        
    IJ.log("✓ Workspace cleanup complete")

def cleanup_memory():
    """
    Clean up memory by forcing garbage collection and clearing unused objects
    """
    try:        # Clear unused objects
        WindowManager.closeAllWindows()
        
        # Force garbage collection
        #System.gc()
        
        IJ.log("✓ Memory cleanup completed")
        
    except Exception as e:
        IJ.log("⚠️ Memory cleanup warning: {}".format(str(e)))
        # Don't raise - this is just optimization

def safe_close_image(imp):
    """
    Safely close an ImagePlus object
    
    Args:
        imp (ImagePlus): ImageJ ImagePlus object to close
    """
    if imp is not None:
        try:
            imp.changes = False  # Prevent "Save Changes?" dialog
            imp.close()
        except Exception as e:
            IJ.log("⚠️ Warning closing image: {}".format(str(e)))

# --- Main Script ---
class BatchProcessingStats:
    def __init__(self):
        self.total_images = 0
        self.processed_images = 0
        self.failed_images = 0
        self.total_particles = 0
        self.classified_particles = 0
        self.errors = []
        
    def add_error(self, image_path, error_msg):
        """Record an error that occurred during processing"""
        self.errors.append((image_path, error_msg))
        self.failed_images += 1
        
    def log_summary(self):
        """Print a summary of the batch processing results"""
        IJ.log("\n=== Batch Processing Summary ===")
        IJ.log("Total images: {}".format(self.total_images))
        IJ.log("Successfully processed: {}".format(self.processed_images))
        IJ.log("Failed: {}".format(self.failed_images))
        success_rate = (self.processed_images * 100.0 / self.total_images) if self.total_images > 0 else 0
        IJ.log("Success rate: {:.1f}%".format(success_rate))
        IJ.log("Total particles found: {}".format(self.total_particles))
        IJ.log("Particles classified: {}".format(self.classified_particles))
        
        if self.errors:
            IJ.log("\nErrors encountered:")
            for img_path, error in self.errors:
                IJ.log("  {}: {}".format(os.path.basename(img_path), error))
                
def main():
    IJ.log("\\Clear")
    IJ.log("Starting Weka classification script...")

    # Step 1: Ensure Weka is available
    if not ensure_weka_in_classpath():
        IJ.log("Failed to ensure Weka is in classpath. Exiting.")
        return
    
    # Step 2: Import Weka classes
    if not import_weka_classes():
        IJ.log("Failed to import Weka classes. Exiting.")
        return
    
    # Step 3: Test Weka functionality
    if not test_weka_functionality():
        IJ.log("Weka functionality test failed. Exiting.")
        return
    
    # Step 4: Validate configuration
    if not validate_configuration():
        IJ.log("Configuration validation failed. Exiting.")
        return
    
    try:
        path_to_images = batch_open_images(import_dir,
                                split_string(file_types),
                                split_string(filters))
        
        stats = BatchProcessingStats()
        stats.total_images = len(path_to_images)
        
        prediction_stats = dict(zip(DEFAULT_CLASS_LABELS, [0] * len(DEFAULT_CLASS_LABELS)))
        all_image_predictions = []  # List to store predictions for each image
        
        IJ.log("Found {} images to process".format(stats.total_images))
        
        for i, img_path in enumerate(path_to_images, 1):
            IJ.log("\n🖼️ Processing image {}/{}: {}".format(i, stats.total_images, os.path.basename(img_path)))
            
            # Open image
            current_img = IJ.openImage(img_path)
            if not current_img:
                stats.add_error(img_path, "Failed to open image")
                continue
                
            current_img.show()
            
            try:
                # Process image
                predictions = process_picture(current_img)
                
                if predictions:
                    # Update statistics
                    img_stats = dict(zip(DEFAULT_CLASS_LABELS, [0] * len(DEFAULT_CLASS_LABELS)))
                    for pred in predictions:
                        if pred in img_stats:
                            img_stats[pred] += 1
                            prediction_stats[pred] += 1
                            stats.classified_particles += 1
                        else:
                            IJ.log("⚠️ Unknown prediction class: {}".format(pred))
                            
                    # Store results
                    all_image_predictions.append((os.path.basename(img_path), img_stats))
                    stats.processed_images += 1
                    stats.total_particles += len(predictions)
                else:
                    stats.add_error(img_path, "No predictions generated")
            
            except Exception as e:
                stats.add_error(img_path, str(e))
            
            finally:
                # Cleanup
                safe_close_image(current_img)
                cleanup_memory()
                
            # Progress update
            if stats.total_images > 1:
                percent_done = (i * 100) // stats.total_images
                IJ.log("📊 Overall progress: {}%".format(percent_done))
        
        # Log batch processing summary
        stats.log_summary()
        
        # Print classification statistics
        IJ.log("\n=== Classification Summary ===")
        total_particles = sum(prediction_stats.values())
        for label, count in prediction_stats.items():
            percentage = (count * 100.0 / total_particles) if total_particles > 0 else 0
            IJ.log("  {}: {} ({:.1f}%)".format(label, count, percentage))
        
        # Write results to CSV
        csv_path = os.path.join(os.path.dirname(import_dir), "particle_predictions.csv")
        write_results_csv(csv_path, all_image_predictions, prediction_stats, DEFAULT_CLASS_LABELS)
        IJ.log("\n✅ Results written to: {}".format(csv_path))
        
    finally:
        # Final cleanup
        if CLEANUP_ON_EXIT:
            IJ.log("\n🧹 Performing final cleanup...")
            cleanup_workspace()
            cleanup_memory()
            
    IJ.log("\n✨ Script completed successfully!")

def process_picture(imp):
    """
    Process a single image through the complete classification pipeline
    
    Args:
        imp (ImagePlus): ImageJ image to process
        
    Returns:
        list: Predictions for each particle, or None if processing failed
    """
    try:
        image_title = imp.getTitle()
        IJ.log("\n📊 Processing image: {}".format(image_title))

        # Step 5: Run macro
        IJ.log("⚙️ Running macro: {}".format(macro_path))
        try:
            IJ.runMacroFile(macro_path, image_title)
        except Exception as e:
            IJ.log("❌ Macro execution failed: {}".format(str(e)))
            return None

        # Step 6: Get results
        rt = ResultsTable.getResultsTable()
        if rt is None or rt.getCounter() == 0:
            IJ.log("❌ No results table found or table is empty")
            return None

        IJ.log("✓ Results table obtained with {} entries".format(rt.getCounter()))

        # Step 7: Extract features
        IJ.log("📈 Extracting particle features...")
        particle_features, feature_names = get_features_from_results_table(rt)
        if not particle_features:
            IJ.log("❌ No valid features extracted")
            return None

        # Step 7.5: Validate and order features for model compatibility
        IJ.log("🔍 Validating feature compatibility...")
        ordered_features, ordered_feature_names, validation_success = validate_and_order_features(
            particle_features, feature_names, REQUIRED_FEATURES)
        
        if not validation_success:
            IJ.log("❌ Feature validation failed - mismatch between macro output and model requirements")
            IJ.log("💡 Tip: Check that your macro generates all required features in the correct format")
            return None

        if not ordered_features:
            IJ.log("❌ No valid features after validation and ordering")
            return None

        # Step 8: Load and analyze model
        IJ.log("🤖 Loading classification model...")
        classifier = load_weka_model(model_path)
        if classifier is None:
            IJ.log("❌ Failed to load Weka model")
            return None

        # Step 8.5: Extract model information
        IJ.log("🔍 Analyzing model structure...")
        model_features, model_classes, analysis_success = analyze_weka_model(classifier)
        
        # Try to auto-configure using model information
        if analysis_success and model_features:
            IJ.log("✨ Model analysis successful - auto-configuring features...")
            if auto_configure_from_model(model_features, model_classes):
                IJ.log("🔄 Re-validating features with model specifications...")
                ordered_features, ordered_feature_names, validation_success = validate_and_order_features(
                    particle_features, feature_names, REQUIRED_FEATURES)
                
                if not validation_success:
                    IJ.log("❌ Feature validation failed after auto-configuration")
                    IJ.log("💡 Tip: Ensure macro output matches the model's training data format")
                    return None
        else:
            IJ.log("Could not auto-configure from model. Using manual configuration...")
        
        # Step 9: Prepare data for classification
        IJ.log("🔧 Preparing data structures...")
        instances = create_weka_instances(ordered_features, ordered_feature_names, model_classes)
        if instances.numInstances() == 0:
            IJ.log("❌ No valid instances created")
            return None

        # Step 10: Perform classification
        IJ.log("🔮 Starting particle classification...")
        predictions = _classify_particles(ordered_features, classifier, ordered_feature_names, model_classes)
        if predictions is None:
            IJ.log("❌ Classification failed")
            return None
            
        IJ.log("✅ Successfully classified {} particles".format(len(predictions)))

        # Step 11: Setup visualization
        IJ.log("🎨 Preparing visualization...")
        class_labels = model_classes if model_classes else DEFAULT_CLASS_LABELS
        colors = [color_class1, color_class2, color_class3]
        color_map = dict(zip(class_labels, colors))

        # Step 12: Visualize results
        try:
            color_particles(predictions, color_map)
            IJ.log("✨ Visualization complete")
        except Exception as e:
            IJ.log("⚠️ Warning: Visualization failed: {}".format(str(e)))
            # Continue since classification was successful
        
        IJ.log("✅ Image processing completed successfully!")
        return predictions
        
    except Exception as e:
        IJ.log("❌ Unexpected error: {}".format(str(e)))
        import traceback
        traceback.print_exc()
        return None

def _classify_particles(particle_features, classifier, feature_names, class_labels):
    """
    Classify particles using the Weka model
    
    Args:
        particle_features (list): List of feature vectors
        classifier: The loaded Weka classifier
        feature_names (list): List of feature names
        class_labels (list): List of class labels
        
    Returns:
        list: Class predictions for each particle
    """
    if not classifier:
        IJ.log("✗ No classifier loaded")
        return None
        
    # Create Weka instances
    instances = _create_weka_instances(particle_features, feature_names, class_labels)
    if not instances or instances.numInstances() == 0:
        return None
        
    # Perform classification
    predictions = []
    try:
        for i in range(instances.numInstances()):
            instance = instances.instance(i)
            prediction_idx = classifier.classifyInstance(instance)
            predicted_label = instances.classAttribute().value(int(prediction_idx))
            predictions.append(predicted_label)
            
        IJ.log("✓ Successfully classified {} particles".format(len(predictions)))
        return predictions
        
    except Exception as e:
        IJ.log("✗ Classification error: {}".format(str(e)))
        return None
            
def _create_weka_instances(feature_vectors, feature_names, class_labels):
    """
    Create Weka Instances object from feature vectors
    
    Args:
        feature_vectors (list): List of feature vectors
        feature_names (list): List of feature names
        class_labels (list): List of class label strings
        
    Returns:
        Instances: Weka Instances object ready for classification
    """
    try:
        attributes = ArrayList()
        
        # Add feature attributes
        for name in feature_names:
            attr = WekaAttribute(name.replace(" ", "_"))
            attributes.add(attr)
            
        # Add class attribute
        class_values = ArrayList()
        for label in class_labels:
            class_values.add(label)
        class_attr = WekaAttribute("Class", class_values)
        attributes.add(class_attr)
        
        # Create instances object
        instances = WekaInstances("ParticleData", attributes, len(feature_vectors))
        instances.setClassIndex(instances.numAttributes() - 1)
        
        # Add feature vectors
        for features in feature_vectors:
            if len(features) != len(feature_names):
                continue
                
            instance = WekaDenseInstance(instances.numAttributes())
            instance.setDataset(instances)
            
            # Set feature values
            for i, value in enumerate(features):
                instance.setValue(i, float(value))
            
            instance.setClassMissing()
            instances.add(instance)
            
        IJ.log("Created {} instances for classification".format(instances.numInstances()))
        return instances
        
    except Exception as e:
        IJ.log("Error creating Weka instances: {}".format(str(e)))
        return None
# --- Run the script ---
if __name__ == '__main__':
    main()