"""
FIJI/ImageJ Confidence Threshold Optimization Script

This script finds the optimal confidence thresholds for Round and Satellite 
classifications to minimize false positives (splattered particles being 
classified as round or satellite).

The script:
1. Loads the shape classification model and test data
2. Tests different threshold combinations
3. Evaluates performance metrics
4. Visualizes results with plots
5. Recommends optimal thresholds

REQUIRED CONFIGURATION:
Set these paths before running:
- SHAPE_MODEL_PATH: Path to the shape classification model
- TEST_DATA_PATH: Path to the .arff test data file

Author: Threshold Optimization Script
Version: 1.0
"""

# === IMPORTS ===
from ij import IJ
from java.io import File
from java.lang import ClassLoader
from java.util import ArrayList
import os
import sys

# === CONFIGURATION ===
# Path to the shape classification model (same as in main script)
SHAPE_MODEL_PATH = "C:\\Studium\\Johann\\Partikeluntersuchungen\\shape_10_06.model"

# Path to test data (.arff file) - SPECIFY YOUR PATH HERE
TEST_DATA_PATH = ""  # Example: "C:\\path\\to\\your\\test_data.arff"

def get_test_data_path_interactive():
    """Interactive dialog to select test data file if path not specified."""
    try:
        from javax.swing import JFileChooser
        from javax.swing.filechooser import FileNameExtensionFilter

        chooser = JFileChooser()
        chooser.setDialogTitle("Select Test Data (.arff file)")

        # Set file filter for ARFF files
        arff_filter = FileNameExtensionFilter("ARFF files (*.arff)", ["arff"])
        chooser.setFileFilter(arff_filter)

        # Set default directory
        if os.path.exists("C:\\Studium\\Johann\\Partikeluntersuchungen"):
            chooser.setCurrentDirectory(File("C:\\Studium\\Johann\\Partikeluntersuchungen"))

        result = chooser.showOpenDialog(None)
        if result == JFileChooser.APPROVE_OPTION:
            selected_file = chooser.getSelectedFile()
            return selected_file.getAbsolutePath()
        else:
            return None
    except:
        return None

# Classification labels (must match the models)
POROSITY_LABELS = ["NonPorous", "Porous"]
SHAPE_LABELS = ["Round", "Satellites", "Splattered"]

# Required features for each classification task (from main script)
POROSITY_REQUIRED_FEATURES = ["total_black_pixels"]
SHAPE_REQUIRED_FEATURES = ["Area", "Circ.", "Solidity", "Round", "Convexity", "FeretRatio"]

# Threshold ranges to test
THRESHOLD_MIN = 0.1
THRESHOLD_MAX = 0.95
THRESHOLD_STEP = 0.05

# === WEKA SETUP ===
def setup_weka():
    """Setup Weka environment and import classes."""
    try:
        # Try to import Weka classes directly
        from weka.core import Attribute as WekaAttribute
        from weka.core import Instances as WekaInstances
        from weka.core import DenseInstance as WekaDenseInstance
        from weka.core import SerializationHelper as WekaSerializationHelper
        from weka.core.converters import ConverterUtils
        return True
    except:
        IJ.log("ERROR: Weka classes not available. Please ensure Weka is properly installed.")
        return False

def load_weka_model(model_path):
    """Load a Weka classification model."""
    try:
        from weka.core import SerializationHelper as WekaSerializationHelper
        model_file = File(model_path)
        if not model_file.exists():
            IJ.log("ERROR: Model file not found: " + model_path)
            return None
        
        classifier = WekaSerializationHelper.read(model_path)
        IJ.log("Model loaded successfully: " + model_path)
        return classifier
    except Exception as e:
        IJ.log("ERROR: Failed to load model: " + str(e))
        return None

def load_test_data(data_path):
    """Load test data from ARFF file."""
    try:
        from weka.core.converters import ConverterUtils
        data_file = File(data_path)
        if not data_file.exists():
            IJ.log("ERROR: Test data file not found: " + data_path)
            return None
        
        data_source = ConverterUtils.DataSource(data_path)
        instances = data_source.getDataSet()
        
        if instances.classIndex() == -1:
            instances.setClassIndex(instances.numAttributes() - 1)
        
        IJ.log("Test data loaded: " + str(instances.numInstances()) + " instances")
        return instances
    except Exception as e:
        IJ.log("ERROR: Failed to load test data: " + str(e))
        return None

def extract_shape_features_from_instances(instances):
    """Extract only required shape features from ARFF instances."""
    try:
        from weka.core import DenseInstance as WekaDenseInstance
        from weka.core import Instances as WekaInstances
        from weka.core import Attribute as WekaAttribute
        from java.util import ArrayList

        # Get feature names and find required indices
        feature_indices = []
        for req_feat in SHAPE_REQUIRED_FEATURES:
            for i in range(instances.numAttributes() - 1):  # Exclude class
                if instances.attribute(i).name().lower() == req_feat.lower():
                    feature_indices.append(i)
                    break
            else:
                IJ.log("ERROR: Missing feature: " + req_feat)
                return None, None

        # Create new dataset with only required features
        attributes = ArrayList()
        for name in SHAPE_REQUIRED_FEATURES:
            attributes.add(WekaAttribute(name.replace(" ", "_")))

        # Add class attribute
        class_labels = ArrayList()
        for label in SHAPE_LABELS:
            class_labels.add(label)
        attributes.add(WekaAttribute("Shape", class_labels))

        shape_instances = WekaInstances("ShapeData", attributes, instances.numInstances())
        shape_instances.setClassIndex(shape_instances.numAttributes() - 1)

        # Copy instances with only required features
        for i in range(instances.numInstances()):
            original = instances.instance(i)
            new_instance = WekaDenseInstance(shape_instances.numAttributes())
            new_instance.setDataset(shape_instances)

            # Copy feature values
            for j, idx in enumerate(feature_indices):
                new_instance.setValue(j, original.value(idx))

            # Copy class
            new_instance.setClassValue(original.classValue())
            shape_instances.add(new_instance)

        IJ.log("Extracted " + str(shape_instances.numInstances()) + " instances with " + str(len(SHAPE_REQUIRED_FEATURES)) + " features")
        return shape_instances, feature_indices

    except Exception as e:
        IJ.log("ERROR: Feature extraction failed: " + str(e))
        return None, None

def validate_test_data(instances):
    """Simple validation of test data."""
    if instances.numInstances() == 0:
        IJ.log("ERROR: No instances in test data")
        return False

    IJ.log("Test data: " + str(instances.numInstances()) + " instances, " +
           str(instances.numAttributes()) + " attributes")

    # Check for required features
    for req_feat in SHAPE_REQUIRED_FEATURES:
        found = False
        for i in range(instances.numAttributes() - 1):
            if instances.attribute(i).name().lower() == req_feat.lower():
                found = True
                break
        if not found:
            IJ.log("ERROR: Missing required feature: " + req_feat)
            return False

    IJ.log("All required features found")
    return True

def classify_with_thresholds(shape_classifier, shape_instances, round_threshold, satellites_threshold):
    """
    Classify shape instances with confidence thresholds applied.
    Uses only the shape classifier since we're optimizing shape classification thresholds.
    Returns: (predictions, true_labels, confidences)
    """
    predictions = []
    true_labels = []
    confidences = []

    for i in range(shape_instances.numInstances()):
        try:
            instance = shape_instances.instance(i)
            true_class_index = int(instance.classValue())

            # Validate class index
            if true_class_index < 0 or true_class_index >= len(SHAPE_LABELS):
                IJ.log("Warning: Invalid true class index " + str(true_class_index) + " for instance " + str(i))
                continue

            true_class = SHAPE_LABELS[true_class_index]

            # Get prediction and confidence distribution
            pred_index = int(shape_classifier.classifyInstance(instance))
            distribution = shape_classifier.distributionForInstance(instance)

            # Validate prediction index
            if pred_index < 0 or pred_index >= len(SHAPE_LABELS):
                IJ.log("Warning: Invalid prediction index " + str(pred_index) + " for instance " + str(i))
                continue

            predicted_class = SHAPE_LABELS[pred_index]
            confidence = distribution[pred_index]

            # Apply confidence thresholds (same logic as main script)
            if predicted_class == "Round" and confidence < round_threshold:
                final_prediction = "Splattered"
            elif predicted_class == "Satellites" and confidence < satellites_threshold:
                final_prediction = "Splattered"
            else:
                final_prediction = predicted_class

            predictions.append(final_prediction)
            true_labels.append(true_class)
            confidences.append(confidence)

        except Exception as e:
            IJ.log("Error processing instance " + str(i) + ": " + str(e))
            continue

    IJ.log("Successfully processed " + str(len(predictions)) + " out of " + str(shape_instances.numInstances()) + " instances")
    return predictions, true_labels, confidences

def calculate_metrics(predictions, true_labels):
    """Calculate performance metrics."""
    # Create confusion matrix
    confusion_matrix = {}
    for true_class in SHAPE_LABELS:
        confusion_matrix[true_class] = {}
        for pred_class in SHAPE_LABELS:
            confusion_matrix[true_class][pred_class] = 0
    
    # Fill confusion matrix
    for true_class, pred_class in zip(true_labels, predictions):
        confusion_matrix[true_class][pred_class] += 1
    
    # Calculate metrics for each class
    metrics = {}
    total_instances = len(predictions)
    
    for class_name in SHAPE_LABELS:
        tp = confusion_matrix[class_name][class_name]  # True positives
        fp = sum(confusion_matrix[other_class][class_name] 
                for other_class in SHAPE_LABELS if other_class != class_name)  # False positives
        fn = sum(confusion_matrix[class_name][other_class] 
                for other_class in SHAPE_LABELS if other_class != class_name)  # False negatives
        
        precision = tp / float(tp + fp) if (tp + fp) > 0 else 0.0
        recall = tp / float(tp + fn) if (tp + fn) > 0 else 0.0
        f1_score = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0.0
        
        metrics[class_name] = {
            'precision': precision,
            'recall': recall,
            'f1_score': f1_score,
            'true_positives': tp,
            'false_positives': fp,
            'false_negatives': fn
        }
    
    # Overall accuracy
    correct_predictions = sum(1 for true_class, pred_class in zip(true_labels, predictions) 
                             if true_class == pred_class)
    accuracy = correct_predictions / float(total_instances)
    
    return metrics, accuracy, confusion_matrix

def optimize_thresholds(shape_classifier, test_data):
    """
    Test different threshold combinations and find optimal values.
    """
    IJ.log("Starting threshold optimization...")
    IJ.log("Testing thresholds from " + str(THRESHOLD_MIN) + " to " + str(THRESHOLD_MAX) +
           " with step " + str(THRESHOLD_STEP))

    # Extract shape features from test data
    IJ.log("Extracting shape features...")
    shape_instances, shape_feature_indices = extract_shape_features_from_instances(test_data)
    if shape_instances is None:
        return [], None, None, -1

    best_metrics = None
    best_thresholds = None
    best_score = -1

    results = []
    
    # Generate threshold values
    threshold_values = []
    current = THRESHOLD_MIN
    while current <= THRESHOLD_MAX:
        threshold_values.append(round(current, 2))
        current += THRESHOLD_STEP
    
    total_combinations = len(threshold_values) * len(threshold_values)
    combination_count = 0
    
    for round_threshold in threshold_values:
        for satellites_threshold in threshold_values:
            combination_count += 1
            
            # Show progress every 20 combinations
            if combination_count % 20 == 0:
                progress = (combination_count / float(total_combinations)) * 100
                IJ.log("Progress: " + str(combination_count) + "/" + str(total_combinations) + 
                       " (" + str(round(progress, 1)) + "%)")
            
            # Test this threshold combination
            predictions, true_labels, confidences = classify_with_thresholds(
                shape_classifier, shape_instances, round_threshold, satellites_threshold)
            
            metrics, accuracy, confusion_matrix = calculate_metrics(predictions, true_labels)
            
            # Calculate composite score (weighted F1 scores)
            # Give higher weight to Splattered class to reduce false positives
            splattered_f1 = metrics['Splattered']['f1_score']
            round_f1 = metrics['Round']['f1_score']
            satellites_f1 = metrics['Satellites']['f1_score']
            
            # Weighted score: emphasize splattered detection and overall accuracy
            composite_score = (0.4 * splattered_f1 + 0.3 * round_f1 + 0.3 * satellites_f1) * accuracy
            
            result = {
                'round_threshold': round_threshold,
                'satellites_threshold': satellites_threshold,
                'accuracy': accuracy,
                'metrics': metrics,
                'composite_score': composite_score,
                'confusion_matrix': confusion_matrix
            }
            results.append(result)
            
            # Check if this is the best so far
            if composite_score > best_score:
                best_score = composite_score
                best_metrics = metrics
                best_thresholds = (round_threshold, satellites_threshold)
    
    IJ.log("Optimization completed!")
    return results, best_thresholds, best_metrics, best_score

def create_heatmap_plot(results):
    """Create a heatmap visualization of the optimization results."""
    try:
        from ij import ImagePlus
        from ij.process import FloatProcessor
        from ij.gui import Plot
        import java.awt.Color as Color

        # Extract unique threshold values
        round_thresholds = sorted(list(set(r['round_threshold'] for r in results)))
        satellites_thresholds = sorted(list(set(r['satellites_threshold'] for r in results)))

        # Create score matrix
        score_matrix = []
        for sat_thresh in satellites_thresholds:
            row = []
            for round_thresh in round_thresholds:
                # Find result for this combination
                score = 0
                for result in results:
                    if (result['round_threshold'] == round_thresh and
                        result['satellites_threshold'] == sat_thresh):
                        score = result['composite_score']
                        break
                row.append(score)
            score_matrix.append(row)

        # Convert to ImageJ FloatProcessor for visualization
        width = len(round_thresholds)
        height = len(satellites_thresholds)

        # Flatten matrix for FloatProcessor
        flat_data = []
        for row in score_matrix:
            flat_data.extend(row)

        fp = FloatProcessor(width, height, flat_data)
        imp = ImagePlus("Threshold Optimization Heatmap", fp)

        # Set calibration for proper axis labels
        cal = imp.getCalibration()
        cal.xOrigin = 0
        cal.yOrigin = 0
        cal.pixelWidth = THRESHOLD_STEP
        cal.pixelHeight = THRESHOLD_STEP
        cal.setXUnit("Round Threshold")
        cal.setYUnit("Satellites Threshold")

        # Apply color LUT for better visualization
        IJ.run(imp, "Fire", "")
        imp.show()

        IJ.log("Heatmap created: Higher values (brighter colors) indicate better performance")
        return True

    except Exception as e:
        IJ.log("Warning: Could not create heatmap visualization: " + str(e))
        return False

def create_performance_plots(results, best_thresholds):
    """Create line plots showing performance metrics."""
    try:
        from ij.gui import Plot
        import java.awt.Color as Color

        # Extract data for plotting
        round_thresholds = []
        satellites_thresholds = []
        accuracies = []
        composite_scores = []
        splattered_f1_scores = []

        for result in results:
            round_thresholds.append(result['round_threshold'])
            satellites_thresholds.append(result['satellites_threshold'])
            accuracies.append(result['accuracy'])
            composite_scores.append(result['composite_score'])
            splattered_f1_scores.append(result['metrics']['Splattered']['f1_score'])

        # Create accuracy vs round threshold plot (fixing satellites threshold at best value)
        best_sat_thresh = best_thresholds[1]
        round_thresh_subset = []
        accuracy_subset = []

        for result in results:
            if abs(result['satellites_threshold'] - best_sat_thresh) < 0.01:  # Close to best satellites threshold
                round_thresh_subset.append(result['round_threshold'])
                accuracy_subset.append(result['accuracy'])

        if round_thresh_subset:
            plot1 = Plot("Accuracy vs Round Threshold", "Round Threshold", "Accuracy")
            plot1.setColor(Color.BLUE)
            plot1.addPoints(round_thresh_subset, accuracy_subset, Plot.LINE)
            plot1.setLimits(min(round_thresh_subset), max(round_thresh_subset),
                           min(accuracy_subset) * 0.95, max(accuracy_subset) * 1.05)
            plot1.show()

        # Create composite score vs satellites threshold plot (fixing round threshold at best value)
        best_round_thresh = best_thresholds[0]
        sat_thresh_subset = []
        score_subset = []

        for result in results:
            if abs(result['round_threshold'] - best_round_thresh) < 0.01:  # Close to best round threshold
                sat_thresh_subset.append(result['satellites_threshold'])
                score_subset.append(result['composite_score'])

        if sat_thresh_subset:
            plot2 = Plot("Composite Score vs Satellites Threshold", "Satellites Threshold", "Composite Score")
            plot2.setColor(Color.RED)
            plot2.addPoints(sat_thresh_subset, score_subset, Plot.LINE)
            plot2.setLimits(min(sat_thresh_subset), max(sat_thresh_subset),
                           min(score_subset) * 0.95, max(score_subset) * 1.05)
            plot2.show()

        IJ.log("Performance plots created")
        return True

    except Exception as e:
        IJ.log("Warning: Could not create performance plots: " + str(e))
        return False

def save_detailed_results(results, best_thresholds, test_data_path, output_path=None):
    """Save detailed results to CSV file."""
    try:
        from java.io import FileWriter, BufferedWriter
        from java.io import File

        if output_path is None:
            output_path = os.path.join(os.path.dirname(test_data_path), "threshold_optimization_results.csv")

        writer = BufferedWriter(FileWriter(File(output_path)))

        # Write header
        header = "Round_Threshold,Satellites_Threshold,Accuracy,Composite_Score,"
        header += "Round_Precision,Round_Recall,Round_F1,"
        header += "Satellites_Precision,Satellites_Recall,Satellites_F1,"
        header += "Splattered_Precision,Splattered_Recall,Splattered_F1\n"
        writer.write(header)

        # Write data
        for result in results:
            row = []
            row.append(str(result['round_threshold']))
            row.append(str(result['satellites_threshold']))
            row.append(str(result['accuracy']))
            row.append(str(result['composite_score']))

            for class_name in SHAPE_LABELS:
                metrics = result['metrics'][class_name]
                row.append(str(metrics['precision']))
                row.append(str(metrics['recall']))
                row.append(str(metrics['f1_score']))

            writer.write(','.join(row) + '\n')

        writer.close()
        IJ.log("Detailed results saved to: " + output_path)
        return True

    except Exception as e:
        IJ.log("Warning: Could not save detailed results: " + str(e))
        return False

def main():
    """Main function to run threshold optimization."""
    IJ.log("\\Clear")
    IJ.log("=" * 60)
    IJ.log("Confidence Threshold Optimization")
    IJ.log("=" * 60)

    # Check if test data path is specified, if not use interactive selection
    test_data_path = TEST_DATA_PATH
    if not test_data_path or not os.path.exists(test_data_path):
        IJ.log("Test data path not specified or file not found.")
        IJ.log("Opening file selection dialog...")
        test_data_path = get_test_data_path_interactive()

        if not test_data_path:
            IJ.log("ERROR: No test data file selected. Please specify TEST_DATA_PATH or select a file.")
            return False

        IJ.log("Selected test data file: " + test_data_path)

    # Setup Weka
    if not setup_weka():
        return False

    # Load shape model and test data
    shape_classifier = load_weka_model(SHAPE_MODEL_PATH)
    if shape_classifier is None:
        return False

    test_data = load_test_data(test_data_path)
    if test_data is None:
        return False

    # Validate test data
    if not validate_test_data(test_data):
        return False

    # Run optimization
    results, best_thresholds, best_metrics, best_score = optimize_thresholds(shape_classifier, test_data)

    # Display results
    IJ.log("")
    IJ.log("=" * 60)
    IJ.log("OPTIMIZATION RESULTS")
    IJ.log("=" * 60)
    IJ.log("Best thresholds found:")
    IJ.log("  Round threshold: " + str(best_thresholds[0]))
    IJ.log("  Satellites threshold: " + str(best_thresholds[1]))
    IJ.log("  Composite score: " + str(round(best_score, 4)))

    IJ.log("")
    IJ.log("Performance with best thresholds:")
    for class_name in SHAPE_LABELS:
        metrics = best_metrics[class_name]
        IJ.log("  " + class_name + ":")
        IJ.log("    Precision: " + str(round(metrics['precision'], 3)))
        IJ.log("    Recall: " + str(round(metrics['recall'], 3)))
        IJ.log("    F1-score: " + str(round(metrics['f1_score'], 3)))

    # Show top 5 performing threshold combinations
    IJ.log("")
    IJ.log("Top 5 performing threshold combinations:")
    sorted_results = sorted(results, key=lambda x: x['composite_score'], reverse=True)
    for i, result in enumerate(sorted_results[:5]):
        IJ.log("  " + str(i+1) + ". Round: " + str(result['round_threshold']) +
               ", Satellites: " + str(result['satellites_threshold']) +
               ", Score: " + str(round(result['composite_score'], 4)) +
               ", Accuracy: " + str(round(result['accuracy'], 3)))

    # Create visualizations
    IJ.log("")
    IJ.log("Creating visualizations...")
    create_heatmap_plot(results)
    create_performance_plots(results, best_thresholds)
    save_detailed_results(results, best_thresholds, test_data_path)

    return True

# === SCRIPT EXECUTION ===
if __name__ == '__main__':
    try:
        success = main()
        if success:
            IJ.log("Optimization completed successfully!")
        else:
            IJ.log("Optimization failed!")
    except Exception as e:
        IJ.log("ERROR: " + str(e))
        import traceback
        traceback.print_exc()
