"""
FIJI/ImageJ Comprehensive Model and Threshold Optimization Script

This script performs complete optimization for particle classification:
1. Tests multiple Weka classifiers for porosity and shape classification
2. Evaluates models using cross-validation (with or without confidence thresholds)
3. Identifies the best models for each task
4. Performs detailed confidence threshold optimization on top 3 shape models
5. Saves optimized models and generates comprehensive reports

Configuration options:
- USE_THRESHOLDS_IN_INITIAL_EVALUATION: Set to False to evaluate pure classifier
  performance without thresholds in Phase 1, True to use default thresholds
- Default confidence thresholds (when enabled):
  * Round confidence threshold: 0.5
  * Satellites confidence threshold: 0.69

REQUIRED CONFIGURATION:
Set these paths before running:
- POROSITY_TRAINING_DATA: Path to porosity training .arff file
- SHAPE_TRAINING_DATA: Path to shape training .arff file

Author: Comprehensive Optimization Script
Version: 1.0
"""

# === IMPORTS ===
from ij import IJ
from java.io import File
from java.util import ArrayList, Random
import os
import sys

# === CONFIGURATION ===
# Paths to training data (.arff files) - SPECIFY YOUR PATHS HERE
POROSITY_TRAINING_DATA = "C:\\Studium\\Johann\\Partikeluntersuchungen\\63um\\Training\\3 Training mit Convexity ohne O1-O3\\porosity_Trainingsdaten_2x3.arff"  # Example: "C:\\path\\to\\porosity_training.arff"
SHAPE_TRAINING_DATA = "C:\\Studium\\Johann\\Partikeluntersuchungen\\63um\\Training\\3 Training mit Convexity ohne O1-O3\\shape_Trainingsdaten_2x3.arff"   

# Output directory for saving results
OUTPUT_DIR = ""  # Will use same dir as training data if empty

# Classification labels
POROSITY_LABELS = ["NonPorous", "Porous"]
SHAPE_LABELS = ["Round", "Satellites", "Splattered"]

# Required features for each classification task
POROSITY_REQUIRED_FEATURES = ["total_black_pixels"]
SHAPE_REQUIRED_FEATURES = ["Area", "Circ.", "Solidity", "Round", "Convexity", "FeretRatio"]

# Model evaluation settings
CV_FOLDS = 10
RANDOM_SEED = 42

# Default confidence thresholds for initial model evaluation
DEFAULT_ROUND_THRESHOLD = 0.5
DEFAULT_SATELLITES_THRESHOLD = 0.69

# Option to use thresholds during initial model evaluation
# Set to False to evaluate pure classifier performance without thresholds
USE_THRESHOLDS_IN_INITIAL_EVALUATION = False

# Threshold optimization settings
THRESHOLD_MIN = 0.1
THRESHOLD_MAX = 0.5
THRESHOLD_STEP = 0.05
TOP_MODELS_FOR_THRESHOLD_OPTIMIZATION = 3

# === WEKA SETUP ===
def setup_weka():
    """Setup Weka environment and import classes."""
    try:
        from weka.core import Instances as WekaInstances
        from weka.core.converters import ConverterUtils
        from weka.classifiers.evaluation import Evaluation
        from weka.classifiers.trees import RandomForest
        from weka.classifiers.functions import SMO
        from weka.classifiers.bayes import NaiveBayes
        from weka.classifiers.lazy import IBk
        from weka.classifiers.meta import Bagging
        from weka.classifiers.trees import J48
        from weka.core import SerializationHelper as WekaSerializationHelper
        return True
    except:
        IJ.log("ERROR: Weka classes not available. Please ensure Weka is properly installed.")
        return False

def load_training_data(data_path):
    """Load training data from ARFF file."""
    try:
        from weka.core.converters import ConverterUtils
        data_file = File(data_path)
        if not data_file.exists():
            IJ.log("ERROR: Training data file not found: " + data_path)
            return None
        
        data_source = ConverterUtils.DataSource(data_path)
        instances = data_source.getDataSet()
        
        if instances.classIndex() == -1:
            instances.setClassIndex(instances.numAttributes() - 1)
        
        IJ.log("Training data loaded: " + str(instances.numInstances()) + " instances")
        return instances
    except Exception as e:
        IJ.log("ERROR: Failed to load training data: " + str(e))
        return None

def get_training_data_path_interactive(task_name):
    """Interactive dialog to select training data file."""
    try:
        from javax.swing import JFileChooser
        from javax.swing.filechooser import FileNameExtensionFilter
        
        chooser = JFileChooser()
        chooser.setDialogTitle("Select " + task_name + " Training Data (.arff file)")
        
        arff_filter = FileNameExtensionFilter("ARFF files (*.arff)", ["arff"])
        chooser.setFileFilter(arff_filter)
        
        if os.path.exists("C:\\Studium\\Johann\\Partikeluntersuchungen"):
            chooser.setCurrentDirectory(File("C:\\Studium\\Johann\\Partikeluntersuchungen"))
        
        result = chooser.showOpenDialog(None)
        if result == JFileChooser.APPROVE_OPTION:
            selected_file = chooser.getSelectedFile()
            return selected_file.getAbsolutePath()
        else:
            return None
    except:
        return None

def extract_required_features(instances, required_features):
    """Extract only required features from instances."""
    try:
        from weka.core import DenseInstance as WekaDenseInstance
        from weka.core import Instances as WekaInstances
        from weka.core import Attribute as WekaAttribute
        from java.util import ArrayList
        
        # Find feature indices
        feature_indices = []
        for req_feat in required_features:
            for i in range(instances.numAttributes() - 1):
                if instances.attribute(i).name().lower() == req_feat.lower():
                    feature_indices.append(i)
                    break
            else:
                IJ.log("ERROR: Missing feature: " + req_feat)
                return None
        
        # Create new dataset
        attributes = ArrayList()
        for name in required_features:
            attributes.add(WekaAttribute(name.replace(" ", "_")))
        
        # Copy class attribute
        class_attr = instances.classAttribute()
        attributes.add(class_attr.copy(class_attr.name()))
        
        new_instances = WekaInstances("FilteredData", attributes, instances.numInstances())
        new_instances.setClassIndex(new_instances.numAttributes() - 1)
        
        # Copy instances
        for i in range(instances.numInstances()):
            original = instances.instance(i)
            new_instance = WekaDenseInstance(new_instances.numAttributes())
            new_instance.setDataset(new_instances)
            
            for j, idx in enumerate(feature_indices):
                new_instance.setValue(j, original.value(idx))
            
            new_instance.setClassValue(original.classValue())
            new_instances.add(new_instance)
        
        return new_instances
        
    except Exception as e:
        IJ.log("ERROR: Feature extraction failed: " + str(e))
        return None

def create_classifiers():
    """Create list of classifiers to test with different parameter combinations."""
    try:
        from weka.classifiers.trees import RandomForest
        from weka.classifiers.functions import SMO
        from weka.classifiers.bayes import NaiveBayes
        from weka.classifiers.lazy import IBk
        from weka.classifiers.meta import Bagging
        from weka.classifiers.trees import J48
        
        classifiers = []
        
        # Random Forest with different parameters
        for num_trees in [10, 50, 100]:
            for max_depth in [0, 5, 10]:  # 0 = unlimited
                rf = RandomForest()
                rf.setNumIterations(num_trees)
                rf.setMaxDepth(max_depth)
                rf.setSeed(RANDOM_SEED)
                classifiers.append(("RandomForest_trees" + str(num_trees) + "_depth" + str(max_depth), rf))
        
        # SVM (SMO) with different kernels
        try:
            smo_poly = SMO()
            classifiers.append(("SVM_Polynomial", smo_poly))
            
            smo_rbf = SMO()
            classifiers.append(("SVM_RBF", smo_rbf))
        except:
            IJ.log("Warning: SVM setup failed, skipping SVM classifiers")
        
        # Naive Bayes
        nb = NaiveBayes()
        classifiers.append(("NaiveBayes", nb))
        
        # k-NN with different k values
        for k in [1, 3, 5, 7]:
            knn = IBk()
            knn.setKNN(k)
            classifiers.append(("kNN_k" + str(k), knn))
        
        # Decision Tree (J48)
        j48 = J48()
        j48.setUnpruned(False)
        classifiers.append(("DecisionTree_Pruned", j48))
        
        j48_unpruned = J48()
        j48_unpruned.setUnpruned(True)
        classifiers.append(("DecisionTree_Unpruned", j48_unpruned))
        
        # Bagging with Random Forest
        bagging = Bagging()
        bagging.setClassifier(RandomForest())
        bagging.setNumIterations(10)
        bagging.setSeed(RANDOM_SEED)
        classifiers.append(("Bagging_RandomForest", bagging))
        
        IJ.log("Created " + str(len(classifiers)) + " classifier configurations")
        return classifiers
        
    except Exception as e:
        IJ.log("ERROR: Failed to create classifiers: " + str(e))
        return []

def evaluate_classifier_with_thresholds(classifier, instances, classifier_name, round_threshold, satellites_threshold):
    """Evaluate a shape classifier using cross-validation with confidence thresholds."""
    try:
        from weka.classifiers.evaluation import Evaluation
        from weka.core import Instances as WekaInstances
        from java.util import Random
        
        # Perform cross-validation with threshold application
        predictions = []
        true_labels = []
        
        random = Random(RANDOM_SEED)
        
        # Manual cross-validation to apply thresholds
        fold_size = instances.numInstances() // CV_FOLDS
        
        for fold in range(CV_FOLDS):
            # Create train/test split
            test_start = fold * fold_size
            test_end = test_start + fold_size if fold < CV_FOLDS - 1 else instances.numInstances()
            
            train_instances = WekaInstances(instances, 0)
            test_instances = WekaInstances(instances, 0)
            
            for i in range(instances.numInstances()):
                if test_start <= i < test_end:
                    test_instances.add(instances.instance(i))
                else:
                    train_instances.add(instances.instance(i))
            
            # Train classifier
            classifier.buildClassifier(train_instances)
            
            # Test with thresholds
            for i in range(test_instances.numInstances()):
                instance = test_instances.instance(i)
                true_class_index = int(instance.classValue())
                true_class = SHAPE_LABELS[true_class_index]
                
                # Get prediction and confidence
                distribution = classifier.distributionForInstance(instance)
                pred_index = max(range(len(distribution)), key=distribution.__getitem__)
                predicted_class = SHAPE_LABELS[pred_index]
                confidence = distribution[pred_index]
                
                # Apply confidence thresholds
                if predicted_class == "Round" and confidence < round_threshold:
                    final_prediction = "Splattered"
                elif predicted_class == "Satellites" and confidence < satellites_threshold:
                    final_prediction = "Splattered"
                else:
                    final_prediction = predicted_class
                
                predictions.append(final_prediction)
                true_labels.append(true_class)
        
        # Calculate metrics
        metrics, accuracy = calculate_metrics_from_predictions(predictions, true_labels)
        
        return {
            'classifier_name': classifier_name,
            'accuracy': accuracy,
            'class_metrics': metrics,
            'round_threshold': round_threshold,
            'satellites_threshold': satellites_threshold
        }
        
    except Exception as e:
        IJ.log("ERROR: Evaluation failed for " + classifier_name + ": " + str(e))
        return None

def calculate_metrics_from_predictions(predictions, true_labels):
    """Calculate performance metrics from predictions and true labels."""
    # Create confusion matrix
    confusion_matrix = {}
    for true_class in SHAPE_LABELS:
        confusion_matrix[true_class] = {}
        for pred_class in SHAPE_LABELS:
            confusion_matrix[true_class][pred_class] = 0

    # Fill confusion matrix
    for true_class, pred_class in zip(true_labels, predictions):
        confusion_matrix[true_class][pred_class] += 1

    # Calculate metrics for each class
    metrics = {}
    total_instances = len(predictions)

    for class_name in SHAPE_LABELS:
        tp = confusion_matrix[class_name][class_name]  # True positives
        fp = sum(confusion_matrix[other_class][class_name]
                for other_class in SHAPE_LABELS if other_class != class_name)  # False positives
        fn = sum(confusion_matrix[class_name][other_class]
                for other_class in SHAPE_LABELS if other_class != class_name)  # False negatives

        precision = tp / float(tp + fp) if (tp + fp) > 0 else 0.0
        recall = tp / float(tp + fn) if (tp + fn) > 0 else 0.0
        f1_score = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0.0

        metrics[class_name] = {
            'precision': precision,
            'recall': recall,
            'f1_score': f1_score
        }

    # Overall accuracy
    correct_predictions = sum(1 for true_class, pred_class in zip(true_labels, predictions)
                             if true_class == pred_class)
    accuracy = correct_predictions / float(total_instances)

    return metrics, accuracy

def evaluate_standard_classifier(classifier, instances, classifier_name):
    """Evaluate a classifier using standard cross-validation (for porosity)."""
    try:
        from weka.classifiers.evaluation import Evaluation
        from java.util import Random

        evaluation = Evaluation(instances)
        random = Random(RANDOM_SEED)
        evaluation.crossValidateModel(classifier, instances, CV_FOLDS, random)

        # Extract metrics
        accuracy = evaluation.pctCorrect() / 100.0

        # Get per-class metrics
        class_metrics = {}
        for i in range(instances.numClasses()):
            class_name = instances.classAttribute().value(i)
            precision = evaluation.precision(i)
            recall = evaluation.recall(i)
            f1_score = evaluation.fMeasure(i)

            class_metrics[class_name] = {
                'precision': precision if not str(precision) == 'NaN' else 0.0,
                'recall': recall if not str(recall) == 'NaN' else 0.0,
                'f1_score': f1_score if not str(f1_score) == 'NaN' else 0.0
            }

        return {
            'classifier_name': classifier_name,
            'accuracy': accuracy,
            'class_metrics': class_metrics
        }

    except Exception as e:
        IJ.log("ERROR: Standard evaluation failed for " + classifier_name + ": " + str(e))
        return None

def find_best_models_for_task(training_data, task_name, required_features, use_thresholds=False):
    """Find the best models for a specific classification task."""
    IJ.log("")
    IJ.log("=" * 50)
    IJ.log("OPTIMIZING MODELS FOR " + task_name.upper())
    IJ.log("=" * 50)

    # Extract required features
    filtered_instances = extract_required_features(training_data, required_features)
    if filtered_instances is None:
        IJ.log("ERROR: Failed to extract required features for " + task_name)
        return []

    # Get classifiers to test
    classifiers = create_classifiers()
    if not classifiers:
        IJ.log("ERROR: No classifiers available for testing")
        return []

    # Evaluate each classifier
    results = []
    total_classifiers = len(classifiers)

    for i, (classifier_name, classifier) in enumerate(classifiers):
        IJ.log("Testing " + str(i+1) + "/" + str(total_classifiers) + ": " + classifier_name)

        try:
            if use_thresholds and task_name == "Shape" and USE_THRESHOLDS_IN_INITIAL_EVALUATION:
                result = evaluate_classifier_with_thresholds(
                    classifier, filtered_instances, classifier_name,
                    DEFAULT_ROUND_THRESHOLD, DEFAULT_SATELLITES_THRESHOLD)
            else:
                result = evaluate_standard_classifier(classifier, filtered_instances, classifier_name)

            if result:
                results.append(result)
                IJ.log("  Accuracy: " + str(round(result['accuracy'], 3)))
        except Exception as e:
            IJ.log("  Failed: " + str(e))

    # Sort by accuracy
    results.sort(key=lambda x: x['accuracy'], reverse=True)

    IJ.log("")
    IJ.log("Top 5 models for " + task_name + ":")
    for i, result in enumerate(results[:5]):
        IJ.log("  " + str(i+1) + ". " + result['classifier_name'] +
               " (Accuracy: " + str(round(result['accuracy'], 3)) + ")")

    return results

def optimize_thresholds_for_model(classifier_name, classifier, shape_instances):
    """Optimize confidence thresholds for a specific shape model."""
    IJ.log("")
    IJ.log("Optimizing thresholds for: " + classifier_name)

    best_result = None
    best_score = -1

    # Generate threshold values
    threshold_values = []
    current = THRESHOLD_MIN
    while current <= THRESHOLD_MAX:
        threshold_values.append(round(current, 2))
        current += THRESHOLD_STEP

    total_combinations = len(threshold_values) * len(threshold_values)
    combination_count = 0

    for round_threshold in threshold_values:
        for satellites_threshold in threshold_values:
            combination_count += 1

            # Show progress every 50 combinations
            if combination_count % 50 == 0:
                progress = (combination_count / float(total_combinations)) * 100
                IJ.log("  Progress: " + str(combination_count) + "/" + str(total_combinations) +
                       " (" + str(round(progress, 1)) + "%)")

            # Test this threshold combination
            result = evaluate_classifier_with_thresholds(
                classifier, shape_instances, classifier_name, round_threshold, satellites_threshold)

            if result:
                # Calculate composite score (emphasize splattered detection)
                splattered_f1 = result['class_metrics']['Splattered']['f1_score']
                round_f1 = result['class_metrics']['Round']['f1_score']
                satellites_f1 = result['class_metrics']['Satellites']['f1_score']

                composite_score = 100 * (0.4 * splattered_f1 + 0.3 * round_f1 + 0.3 * satellites_f1) * result['accuracy']

                if composite_score > best_score:
                    best_score = composite_score
                    best_result = result
                    best_result['composite_score'] = composite_score

    if best_result:
        IJ.log("  Best thresholds: Round=" + str(best_result['round_threshold']) +
               ", Satellites=" + str(best_result['satellites_threshold']))
        IJ.log("  Best score: " + str(round(best_result['composite_score'], 4)))

    return best_result

def save_comprehensive_results(porosity_results, shape_results, threshold_results, output_dir):
    """Save comprehensive results to CSV files."""
    try:
        from java.io import FileWriter, BufferedWriter
        from java.io import File

        # Save model comparison results
        model_report_path = os.path.join(output_dir, "model_comparison_report.csv")
        writer = BufferedWriter(FileWriter(File(model_report_path)))

        header = "Task,Rank,Classifier,Accuracy,Class,Precision,Recall,F1_Score\n"
        writer.write(header)

        # Write porosity results
        for rank, result in enumerate(porosity_results, 1):
            for class_name, metrics in result['class_metrics'].items():
                row = "Porosity," + str(rank) + "," + result['classifier_name'] + ","
                row += str(result['accuracy']) + "," + class_name + ","
                row += str(metrics['precision']) + "," + str(metrics['recall']) + ","
                row += str(metrics['f1_score']) + "\n"
                writer.write(row)

        # Write shape results
        for rank, result in enumerate(shape_results, 1):
            for class_name, metrics in result['class_metrics'].items():
                row = "Shape," + str(rank) + "," + result['classifier_name'] + ","
                row += str(result['accuracy']) + "," + class_name + ","
                row += str(metrics['precision']) + "," + str(metrics['recall']) + ","
                row += str(metrics['f1_score']) + "\n"
                writer.write(row)

        writer.close()
        IJ.log("Model comparison report saved: " + model_report_path)

        # Save threshold optimization results
        threshold_report_path = os.path.join(output_dir, "threshold_optimization_report.csv")
        writer = BufferedWriter(FileWriter(File(threshold_report_path)))

        header = "Classifier,Round_Threshold,Satellites_Threshold,Accuracy,Composite_Score,"
        header += "Round_Precision,Round_Recall,Round_F1,"
        header += "Satellites_Precision,Satellites_Recall,Satellites_F1,"
        header += "Splattered_Precision,Splattered_Recall,Splattered_F1\n"
        writer.write(header)

        for result in threshold_results:
            if result:
                row = result['classifier_name'] + ","
                row += str(result['round_threshold']) + "," + str(result['satellites_threshold']) + ","
                row += str(result['accuracy']) + "," + str(result['composite_score']) + ","

                for class_name in SHAPE_LABELS:
                    metrics = result['class_metrics'][class_name]
                    row += str(metrics['precision']) + "," + str(metrics['recall']) + ","
                    row += str(metrics['f1_score']) + ","

                writer.write(row.rstrip(",") + "\n")

        writer.close()
        IJ.log("Threshold optimization report saved: " + threshold_report_path)
        return True

    except Exception as e:
        IJ.log("ERROR: Failed to save results: " + str(e))
        return False

def save_best_models(porosity_results, shape_results, threshold_results,
                    porosity_data, shape_data, output_dir):
    """Train and save the best models."""
    try:
        from weka.core import SerializationHelper as WekaSerializationHelper

        # Save best porosity model
        if porosity_results:
            best_porosity = porosity_results[0]
            classifiers = create_classifiers()
            classifier_dict = dict(classifiers)

            if best_porosity['classifier_name'] in classifier_dict:
                porosity_filtered = extract_required_features(porosity_data, POROSITY_REQUIRED_FEATURES)
                classifier = classifier_dict[best_porosity['classifier_name']]
                classifier.buildClassifier(porosity_filtered)

                model_path = os.path.join(output_dir, "best_porosity_model.model")
                WekaSerializationHelper.write(model_path, classifier)
                IJ.log("Best porosity model saved: " + model_path)

        # Save best shape models with optimized thresholds
        if threshold_results:
            best_threshold_result = max(threshold_results, key=lambda x: x['composite_score'] if x else 0)
            if best_threshold_result:
                classifiers = create_classifiers()
                classifier_dict = dict(classifiers)

                if best_threshold_result['classifier_name'] in classifier_dict:
                    shape_filtered = extract_required_features(shape_data, SHAPE_REQUIRED_FEATURES)
                    classifier = classifier_dict[best_threshold_result['classifier_name']]
                    classifier.buildClassifier(shape_filtered)

                    model_path = os.path.join(output_dir, "best_shape_model.model")
                    WekaSerializationHelper.write(model_path, classifier)
                    IJ.log("Best shape model saved: " + model_path)

                    # Save threshold configuration
                    config_path = os.path.join(output_dir, "best_thresholds.txt")
                    with open(config_path, 'w') as f:
                        f.write("Best Shape Model: " + best_threshold_result['classifier_name'] + "\n")
                        f.write("Round Threshold: " + str(best_threshold_result['round_threshold']) + "\n")
                        f.write("Satellites Threshold: " + str(best_threshold_result['satellites_threshold']) + "\n")
                        f.write("Accuracy: " + str(best_threshold_result['accuracy']) + "\n")
                        f.write("Composite Score: " + str(best_threshold_result['composite_score']) + "\n")
                    IJ.log("Best thresholds saved: " + config_path)

        return True

    except Exception as e:
        IJ.log("ERROR: Failed to save models: " + str(e))
        return False

def main():
    """Main function to run comprehensive model and threshold optimization."""
    IJ.log("\\Clear")
    IJ.log("=" * 60)
    IJ.log("Comprehensive Model and Threshold Optimization")
    IJ.log("=" * 60)
    IJ.log("Configuration:")
    IJ.log("  Use thresholds in initial evaluation: " + str(USE_THRESHOLDS_IN_INITIAL_EVALUATION))
    if USE_THRESHOLDS_IN_INITIAL_EVALUATION:
        IJ.log("  Default round threshold: " + str(DEFAULT_ROUND_THRESHOLD))
        IJ.log("  Default satellites threshold: " + str(DEFAULT_SATELLITES_THRESHOLD))
    else:
        IJ.log("  Initial evaluation will use pure classifier performance")

    # Setup Weka
    if not setup_weka():
        return False

    # Get training data paths
    porosity_data_path = POROSITY_TRAINING_DATA
    if not porosity_data_path or not os.path.exists(porosity_data_path):
        IJ.log("Porosity training data not specified. Opening file selection...")
        porosity_data_path = get_training_data_path_interactive("Porosity")
        if not porosity_data_path:
            IJ.log("ERROR: No porosity training data selected")
            return False

    shape_data_path = SHAPE_TRAINING_DATA
    if not shape_data_path or not os.path.exists(shape_data_path):
        IJ.log("Shape training data not specified. Opening file selection...")
        shape_data_path = get_training_data_path_interactive("Shape")
        if not shape_data_path:
            IJ.log("ERROR: No shape training data selected")
            return False

    # Set output directory
    output_dir = OUTPUT_DIR
    if not output_dir:
        output_dir = os.path.dirname(porosity_data_path)

    # Load training data
    IJ.log("Loading training data...")
    porosity_data = load_training_data(porosity_data_path)
    if porosity_data is None:
        return False

    shape_data = load_training_data(shape_data_path)
    if shape_data is None:
        return False

    # PHASE 1: Find best models
    IJ.log("")
    IJ.log("=" * 60)
    if USE_THRESHOLDS_IN_INITIAL_EVALUATION:
        IJ.log("PHASE 1: MODEL SELECTION WITH DEFAULT THRESHOLDS")
    else:
        IJ.log("PHASE 1: MODEL SELECTION WITHOUT THRESHOLDS")
    IJ.log("=" * 60)

    porosity_results = find_best_models_for_task(porosity_data, "Porosity", POROSITY_REQUIRED_FEATURES, False)
    shape_results = find_best_models_for_task(shape_data, "Shape", SHAPE_REQUIRED_FEATURES, USE_THRESHOLDS_IN_INITIAL_EVALUATION)

    if not porosity_results or not shape_results:
        IJ.log("ERROR: Model evaluation failed")
        return False

    # PHASE 2: Detailed threshold optimization for top shape models
    IJ.log("")
    IJ.log("=" * 60)
    IJ.log("PHASE 2: THRESHOLD OPTIMIZATION FOR TOP SHAPE MODELS")
    IJ.log("=" * 60)

    top_shape_models = shape_results[:TOP_MODELS_FOR_THRESHOLD_OPTIMIZATION]
    shape_filtered = extract_required_features(shape_data, SHAPE_REQUIRED_FEATURES)

    threshold_results = []
    classifiers = create_classifiers()
    classifier_dict = dict(classifiers)

    for i, shape_result in enumerate(top_shape_models):
        IJ.log("")
        IJ.log("Optimizing model " + str(i+1) + "/" + str(len(top_shape_models)) + ": " +
               shape_result['classifier_name'])

        if shape_result['classifier_name'] in classifier_dict:
            classifier = classifier_dict[shape_result['classifier_name']]
            optimized_result = optimize_thresholds_for_model(
                shape_result['classifier_name'], classifier, shape_filtered)
            threshold_results.append(optimized_result)

    # PHASE 3: Save results and models
    IJ.log("")
    IJ.log("=" * 60)
    IJ.log("PHASE 3: SAVING RESULTS AND MODELS")
    IJ.log("=" * 60)

    save_comprehensive_results(porosity_results, shape_results, threshold_results, output_dir)
    save_best_models(porosity_results, shape_results, threshold_results,
                    porosity_data, shape_data, output_dir)

    # Display final recommendations
    IJ.log("")
    IJ.log("=" * 60)
    IJ.log("FINAL RECOMMENDATIONS")
    IJ.log("=" * 60)

    if porosity_results:
        best_porosity = porosity_results[0]
        IJ.log("Best Porosity Model: " + best_porosity['classifier_name'])
        IJ.log("  Accuracy: " + str(round(best_porosity['accuracy'], 3)))

    if threshold_results:
        best_threshold_result = max(threshold_results, key=lambda x: x['composite_score'] if x else 0)
        if best_threshold_result:
            IJ.log("")
            IJ.log("Best Shape Model with Optimized Thresholds:")
            IJ.log("  Model: " + best_threshold_result['classifier_name'])
            IJ.log("  Round threshold: " + str(best_threshold_result['round_threshold']))
            IJ.log("  Satellites threshold: " + str(best_threshold_result['satellites_threshold']))
            IJ.log("  Accuracy: " + str(round(best_threshold_result['accuracy'], 3)))
            IJ.log("  Composite score: " + str(round(best_threshold_result['composite_score'], 4)))

    return True

# === SCRIPT EXECUTION ===
if __name__ == '__main__':
    try:
        success = main()
        if success:
            IJ.log("Comprehensive optimization completed successfully!")
        else:
            IJ.log("Comprehensive optimization failed!")
    except Exception as e:
        IJ.log("ERROR: " + str(e))
        import traceback
        traceback.print_exc()
