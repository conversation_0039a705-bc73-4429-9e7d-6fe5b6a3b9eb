"""
FIJI/ImageJ Model Selection and Optimization Script

This script finds the best Weka models and their optimal settings for 
porosity and shape classification by testing multiple algorithms with 
different parameter combinations.

The script:
1. Loads training data from ARFF files
2. Tests multiple Weka classifiers (<PERSON> Forest, SVM, Naive <PERSON>, etc.)
3. Performs cross-validation for each model configuration
4. Evaluates performance metrics (accuracy, precision, recall, F1-score)
5. Finds optimal parameters for each classifier
6. Recommends the best models for each classification task
7. Saves the best models and generates performance reports

REQUIRED CONFIGURATION:
Set these paths before running:
- POROSITY_TRAINING_DATA: Path to porosity training .arff file
- SHAPE_TRAINING_DATA: Path to shape training .arff file

Author: Model Selection Script
Version: 1.0
"""

# === IMPORTS ===
from ij import IJ
from java.io import File
from java.util import ArrayList, Random
import os
import sys

# === CONFIGURATION ===
# Paths to training data (.arff files) - SPECIFY YOUR PATHS HERE
POROSITY_TRAINING_DATA = "C:\\Studium\\Johann\\Partikeluntersuchungen\\63um\\Training\\3 Training mit Convexity ohne O1-O3\\Trainingsdaten_2x3.arff"  # Example: "C:\\path\\to\\porosity_training.arff"
SHAPE_TRAINING_DATA = "C:\\Studium\\Johann\\Partikeluntersuchungen\\63um\\Training\\3 Training mit Convexity ohne O1-O3\\Trainingsdaten_2x3.arff"     

# Output directory for saving best models
OUTPUT_DIR = ""  # Example: "C:\\path\\to\\output\\" - will use same dir as training data if empty

# Classification labels
POROSITY_LABELS = ["NonPorous", "Porous"]
SHAPE_LABELS = ["Round", "Satellites", "Splattered"]

# Required features for each classification task
POROSITY_REQUIRED_FEATURES = ["total_black_pixels"]
SHAPE_REQUIRED_FEATURES = ["Area", "Circ.", "Solidity", "Round", "Convexity", "FeretRatio"]

# Cross-validation settings
CV_FOLDS = 10
RANDOM_SEED = 42

# === WEKA SETUP ===
def setup_weka():
    """Setup Weka environment and import classes."""
    try:
        from weka.core import Instances as WekaInstances
        from weka.core.converters import ConverterUtils
        from weka.classifiers.evaluation import Evaluation
        from weka.classifiers.trees import RandomForest
        from weka.classifiers.functions import SMO
        from weka.classifiers.bayes import NaiveBayes
        from weka.classifiers.lazy import IBk
        from weka.classifiers.meta import Bagging
        from weka.core import SerializationHelper as WekaSerializationHelper
        return True
    except:
        IJ.log("ERROR: Weka classes not available. Please ensure Weka is properly installed.")
        return False

def load_training_data(data_path):
    """Load training data from ARFF file."""
    try:
        from weka.core.converters import ConverterUtils
        data_file = File(data_path)
        if not data_file.exists():
            IJ.log("ERROR: Training data file not found: " + data_path)
            return None
        
        data_source = ConverterUtils.DataSource(data_path)
        instances = data_source.getDataSet()
        
        if instances.classIndex() == -1:
            instances.setClassIndex(instances.numAttributes() - 1)
        
        IJ.log("Training data loaded: " + str(instances.numInstances()) + " instances")
        return instances
    except Exception as e:
        IJ.log("ERROR: Failed to load training data: " + str(e))
        return None

def get_training_data_path_interactive(task_name):
    """Interactive dialog to select training data file."""
    try:
        from javax.swing import JFileChooser
        from javax.swing.filechooser import FileNameExtensionFilter
        
        chooser = JFileChooser()
        chooser.setDialogTitle("Select " + task_name + " Training Data (.arff file)")
        
        arff_filter = FileNameExtensionFilter("ARFF files (*.arff)", ["arff"])
        chooser.setFileFilter(arff_filter)
        
        if os.path.exists("C:\\Studium\\Johann\\Partikeluntersuchungen"):
            chooser.setCurrentDirectory(File("C:\\Studium\\Johann\\Partikeluntersuchungen"))
        
        result = chooser.showOpenDialog(None)
        if result == JFileChooser.APPROVE_OPTION:
            selected_file = chooser.getSelectedFile()
            return selected_file.getAbsolutePath()
        else:
            return None
    except:
        return None

def create_classifiers():
    """Create list of classifiers to test with different parameter combinations."""
    try:
        from weka.classifiers.trees import RandomForest
        from weka.classifiers.functions import SMO
        from weka.classifiers.bayes import NaiveBayes
        from weka.classifiers.lazy import IBk
        from weka.classifiers.meta import Bagging
        from weka.classifiers.trees import J48
        
        classifiers = []
        
        # Random Forest with different parameters
        for num_trees in [10, 50, 100]:
            for max_depth in [0, 5, 10]:  # 0 = unlimited
                rf = RandomForest()
                rf.setNumIterations(num_trees)
                rf.setMaxDepth(max_depth)
                rf.setSeed(RANDOM_SEED)
                classifiers.append(("RandomForest_trees" + str(num_trees) + "_depth" + str(max_depth), rf))
        
        # SVM (SMO) with different kernels
        try:
            smo_poly = SMO()
            smo_poly.setKernel(smo_poly.getKernel())  # Default polynomial
            classifiers.append(("SVM_Polynomial", smo_poly))
            
            smo_rbf = SMO()
            # Note: RBF kernel setup may vary by Weka version
            classifiers.append(("SVM_RBF", smo_rbf))
        except:
            IJ.log("Warning: SVM setup failed, skipping SVM classifiers")
        
        # Naive Bayes
        nb = NaiveBayes()
        classifiers.append(("NaiveBayes", nb))
        
        # k-NN with different k values
        for k in [1, 3, 5, 7]:
            knn = IBk()
            knn.setKNN(k)
            classifiers.append(("kNN_k" + str(k), knn))
        
        # Decision Tree (J48)
        j48 = J48()
        j48.setUnpruned(False)
        classifiers.append(("DecisionTree_Pruned", j48))
        
        j48_unpruned = J48()
        j48_unpruned.setUnpruned(True)
        classifiers.append(("DecisionTree_Unpruned", j48_unpruned))
        
        # Bagging with Random Forest
        bagging = Bagging()
        bagging.setClassifier(RandomForest())
        bagging.setNumIterations(10)
        bagging.setSeed(RANDOM_SEED)
        classifiers.append(("Bagging_RandomForest", bagging))
        
        IJ.log("Created " + str(len(classifiers)) + " classifier configurations")
        return classifiers
        
    except Exception as e:
        IJ.log("ERROR: Failed to create classifiers: " + str(e))
        return []

def evaluate_classifier(classifier, instances, classifier_name):
    """Evaluate a classifier using cross-validation."""
    try:
        from weka.classifiers.evaluation import Evaluation
        from java.util import Random
        
        evaluation = Evaluation(instances)
        random = Random(RANDOM_SEED)
        evaluation.crossValidateModel(classifier, instances, CV_FOLDS, random)
        
        # Extract metrics
        accuracy = evaluation.pctCorrect() / 100.0
        
        # Get per-class metrics
        class_metrics = {}
        for i in range(instances.numClasses()):
            class_name = instances.classAttribute().value(i)
            precision = evaluation.precision(i)
            recall = evaluation.recall(i)
            f1_score = evaluation.fMeasure(i)
            
            class_metrics[class_name] = {
                'precision': precision if not str(precision) == 'NaN' else 0.0,
                'recall': recall if not str(recall) == 'NaN' else 0.0,
                'f1_score': f1_score if not str(f1_score) == 'NaN' else 0.0
            }
        
        # Calculate weighted F1 score
        weighted_f1 = 0.0
        total_instances = instances.numInstances()
        for i in range(instances.numClasses()):
            class_count = 0
            for j in range(instances.numInstances()):
                if int(instances.instance(j).classValue()) == i:
                    class_count += 1
            weight = class_count / float(total_instances)
            class_name = instances.classAttribute().value(i)
            weighted_f1 += weight * class_metrics[class_name]['f1_score']
        
        result = {
            'classifier_name': classifier_name,
            'accuracy': accuracy,
            'weighted_f1': weighted_f1,
            'class_metrics': class_metrics,
            'confusion_matrix': evaluation.confusionMatrix()
        }
        
        return result
        
    except Exception as e:
        IJ.log("ERROR: Evaluation failed for " + classifier_name + ": " + str(e))
        return None

def find_best_models_for_task(training_data, task_name, required_features):
    """Find the best models for a specific classification task."""
    IJ.log("")
    IJ.log("=" * 50)
    IJ.log("OPTIMIZING MODELS FOR " + task_name.upper())
    IJ.log("=" * 50)
    
    # Extract required features (same logic as other scripts)
    filtered_instances = extract_required_features(training_data, required_features)
    if filtered_instances is None:
        IJ.log("ERROR: Failed to extract required features for " + task_name)
        return []
    
    # Get classifiers to test
    classifiers = create_classifiers()
    if not classifiers:
        IJ.log("ERROR: No classifiers available for testing")
        return []
    
    # Evaluate each classifier
    results = []
    total_classifiers = len(classifiers)
    
    for i, (classifier_name, classifier) in enumerate(classifiers):
        IJ.log("Testing " + str(i+1) + "/" + str(total_classifiers) + ": " + classifier_name)
        
        try:
            result = evaluate_classifier(classifier, filtered_instances, classifier_name)
            if result:
                results.append(result)
                IJ.log("  Accuracy: " + str(round(result['accuracy'], 3)) + 
                       ", Weighted F1: " + str(round(result['weighted_f1'], 3)))
        except Exception as e:
            IJ.log("  Failed: " + str(e))
    
    # Sort by weighted F1 score
    results.sort(key=lambda x: x['weighted_f1'], reverse=True)
    
    IJ.log("")
    IJ.log("Top 5 models for " + task_name + ":")
    for i, result in enumerate(results[:5]):
        IJ.log("  " + str(i+1) + ". " + result['classifier_name'] + 
               " (F1: " + str(round(result['weighted_f1'], 3)) + 
               ", Acc: " + str(round(result['accuracy'], 3)) + ")")
    
    return results

def extract_required_features(instances, required_features):
    """Extract only required features from instances."""
    try:
        from weka.core import DenseInstance as WekaDenseInstance
        from weka.core import Instances as WekaInstances
        from weka.core import Attribute as WekaAttribute
        from java.util import ArrayList
        
        # Find feature indices
        feature_indices = []
        for req_feat in required_features:
            for i in range(instances.numAttributes() - 1):
                if instances.attribute(i).name().lower() == req_feat.lower():
                    feature_indices.append(i)
                    break
            else:
                IJ.log("ERROR: Missing feature: " + req_feat)
                return None
        
        # Create new dataset
        attributes = ArrayList()
        for name in required_features:
            attributes.add(WekaAttribute(name.replace(" ", "_")))
        
        # Copy class attribute
        class_attr = instances.classAttribute()
        attributes.add(class_attr.copy(class_attr.name()))
        
        new_instances = WekaInstances("FilteredData", attributes, instances.numInstances())
        new_instances.setClassIndex(new_instances.numAttributes() - 1)
        
        # Copy instances
        for i in range(instances.numInstances()):
            original = instances.instance(i)
            new_instance = WekaDenseInstance(new_instances.numAttributes())
            new_instance.setDataset(new_instances)
            
            for j, idx in enumerate(feature_indices):
                new_instance.setValue(j, original.value(idx))
            
            new_instance.setClassValue(original.classValue())
            new_instances.add(new_instance)
        
        return new_instances

    except Exception as e:
        IJ.log("ERROR: Feature extraction failed: " + str(e))
        return None

def save_best_model(classifier_name, classifier, training_data, task_name, output_dir):
    """Train and save the best model."""
    try:
        from weka.core import SerializationHelper as WekaSerializationHelper

        # Train the classifier on full dataset
        classifier.buildClassifier(training_data)

        # Generate filename
        safe_name = classifier_name.replace(" ", "_").replace("/", "_")
        model_filename = task_name.lower() + "_best_" + safe_name + ".model"
        model_path = os.path.join(output_dir, model_filename)

        # Save model
        WekaSerializationHelper.write(model_path, classifier)
        IJ.log("Best " + task_name + " model saved: " + model_path)
        return model_path

    except Exception as e:
        IJ.log("ERROR: Failed to save model: " + str(e))
        return None

def save_results_report(porosity_results, shape_results, output_dir):
    """Save detailed results report to CSV."""
    try:
        from java.io import FileWriter, BufferedWriter
        from java.io import File

        report_path = os.path.join(output_dir, "model_evaluation_report.csv")
        writer = BufferedWriter(FileWriter(File(report_path)))

        # Write header
        header = "Task,Rank,Classifier,Accuracy,Weighted_F1,Class,Precision,Recall,F1_Score\n"
        writer.write(header)

        # Write porosity results
        for rank, result in enumerate(porosity_results, 1):
            for class_name, metrics in result['class_metrics'].items():
                row = "Porosity," + str(rank) + "," + result['classifier_name'] + ","
                row += str(result['accuracy']) + "," + str(result['weighted_f1']) + ","
                row += class_name + "," + str(metrics['precision']) + ","
                row += str(metrics['recall']) + "," + str(metrics['f1_score']) + "\n"
                writer.write(row)

        # Write shape results
        for rank, result in enumerate(shape_results, 1):
            for class_name, metrics in result['class_metrics'].items():
                row = "Shape," + str(rank) + "," + result['classifier_name'] + ","
                row += str(result['accuracy']) + "," + str(result['weighted_f1']) + ","
                row += class_name + "," + str(metrics['precision']) + ","
                row += str(metrics['recall']) + "," + str(metrics['f1_score']) + "\n"
                writer.write(row)

        writer.close()
        IJ.log("Detailed report saved: " + report_path)
        return True

    except Exception as e:
        IJ.log("ERROR: Failed to save report: " + str(e))
        return False

def main():
    """Main function to run model selection and optimization."""
    IJ.log("\\Clear")
    IJ.log("=" * 60)
    IJ.log("Model Selection and Optimization")
    IJ.log("=" * 60)

    # Setup Weka
    if not setup_weka():
        return False

    # Get training data paths
    porosity_data_path = POROSITY_TRAINING_DATA
    if not porosity_data_path or not os.path.exists(porosity_data_path):
        IJ.log("Porosity training data not specified. Opening file selection...")
        porosity_data_path = get_training_data_path_interactive("Porosity")
        if not porosity_data_path:
            IJ.log("ERROR: No porosity training data selected")
            return False

    shape_data_path = SHAPE_TRAINING_DATA
    if not shape_data_path or not os.path.exists(shape_data_path):
        IJ.log("Shape training data not specified. Opening file selection...")
        shape_data_path = get_training_data_path_interactive("Shape")
        if not shape_data_path:
            IJ.log("ERROR: No shape training data selected")
            return False

    # Set output directory
    output_dir = OUTPUT_DIR
    if not output_dir:
        output_dir = os.path.dirname(porosity_data_path)

    # Load training data
    IJ.log("Loading training data...")
    porosity_data = load_training_data(porosity_data_path)
    if porosity_data is None:
        return False

    shape_data = load_training_data(shape_data_path)
    if shape_data is None:
        return False

    # Find best models for each task
    porosity_results = find_best_models_for_task(porosity_data, "Porosity", POROSITY_REQUIRED_FEATURES)
    shape_results = find_best_models_for_task(shape_data, "Shape", SHAPE_REQUIRED_FEATURES)

    if not porosity_results or not shape_results:
        IJ.log("ERROR: Model evaluation failed")
        return False

    # Save best models
    IJ.log("")
    IJ.log("=" * 60)
    IJ.log("SAVING BEST MODELS")
    IJ.log("=" * 60)

    # Get best classifiers and retrain on filtered data
    best_porosity = porosity_results[0]
    best_shape = shape_results[0]

    # Recreate and train best classifiers
    classifiers = create_classifiers()
    classifier_dict = dict(classifiers)

    if best_porosity['classifier_name'] in classifier_dict:
        porosity_filtered = extract_required_features(porosity_data, POROSITY_REQUIRED_FEATURES)
        save_best_model(best_porosity['classifier_name'],
                       classifier_dict[best_porosity['classifier_name']],
                       porosity_filtered, "Porosity", output_dir)

    if best_shape['classifier_name'] in classifier_dict:
        shape_filtered = extract_required_features(shape_data, SHAPE_REQUIRED_FEATURES)
        save_best_model(best_shape['classifier_name'],
                       classifier_dict[best_shape['classifier_name']],
                       shape_filtered, "Shape", output_dir)

    # Save detailed report
    save_results_report(porosity_results, shape_results, output_dir)

    # Display final recommendations
    IJ.log("")
    IJ.log("=" * 60)
    IJ.log("FINAL RECOMMENDATIONS")
    IJ.log("=" * 60)
    IJ.log("Best Porosity Model: " + best_porosity['classifier_name'])
    IJ.log("  Accuracy: " + str(round(best_porosity['accuracy'], 3)))
    IJ.log("  Weighted F1: " + str(round(best_porosity['weighted_f1'], 3)))

    IJ.log("")
    IJ.log("Best Shape Model: " + best_shape['classifier_name'])
    IJ.log("  Accuracy: " + str(round(best_shape['accuracy'], 3)))
    IJ.log("  Weighted F1: " + str(round(best_shape['weighted_f1'], 3)))

    return True

# === SCRIPT EXECUTION ===
if __name__ == '__main__':
    try:
        success = main()
        if success:
            IJ.log("Model selection completed successfully!")
        else:
            IJ.log("Model selection failed!")
    except Exception as e:
        IJ.log("ERROR: " + str(e))
        import traceback
        traceback.print_exc()
