# Augment AI Assistant - User Preferences Documentation

**Created**: December 2024  
**Purpose**: Complete reference for recreating Augment preferences on new installations  
**Context**: ImageJ/FIJI particle analysis workflow development

---

## 📋 **Code Style Preferences**

### **File Organization**
- **Single File Preference**: Keep code in single files, strictly avoid dividing into multiple files
- **Consolidation**: Prefer function consolidation over verbose implementations
- **Compactness**: Favor compact, efficient code structure

### **Logging Philosophy**
- **Minimal Logging**: Only initialization and final statistics in ImageJ/FIJI scripts
- **No Verbose Output**: Avoid step-by-step debugging in production scripts
- **Clean Output**: Focus on essential information only

---

## 🔧 **ImageJ/FIJI Specific Preferences**

### **Jython 2.7 Compatibility**
- **NO f-string literals**: Never use f"text {variable}" syntax (Python 3.6+ feature)
- **Use .format() method**: Always use "text {}".format(variable) for ALL string formatting
- **Apply to**: Logging, file paths, progress indicators, error messages
- **Reason**: Jython 2.7 doesn't support f-string syntax

### **Unicode Handling Best Practices**
- **Core Principle**: "Don't convert Unicode - just use it directly"
- **Preserve Original**: Use original Unicode strings without conversion
- **Image Titles**: Preserve with imp.getTitle()
- **File Operations**: Use os.listdir() with original filenames
- **Logging**: Use IJ.log() directly with Unicode strings
- **CSV Export**: Simple FileWriter for CSV export
- **Avoid**: "safe_unicode_str()" functions or Unicode conversion
- **Example Files**: Handle "particle_10µm.tif" properly

### **Visual Elements**
- **No Emojis**: IJ.log doesn't display emojis correctly in ImageJ/FIJI
- **Clear Separation**: Visual separation in logs after processing each image
- **Professional Output**: Clean, scientific presentation

### **Performance Configuration**
- **Background Processing**: Include SHOW_IMAGES_DURING_PROCESSING option
- **Default**: Disable automatic image display during processing
- **Purpose**: Better performance and unattended operation
- **Maintain**: All processing functionality regardless of display mode

---

## 🖥️ **Cross-Platform Compatibility**

### **Path Handling**
- **Universal Paths**: Handle Windows, macOS, and Linux path formats
- **File Permissions**: Account for different permission systems
- **Dependencies**: Ensure cross-platform dependency management
- **Configuration**: Cross-platform configuration file handling

### **Folder Selection**
- **Interactive Dialogs**: Use IJ.getDirectory() or JFileChooser
- **Error Handling**: Proper error handling for dialog failures
- **Unicode Support**: Handle Unicode in folder/file names
- **Default Starting Directory**: C:\Studium\Johann\Partikeluntersuchungen
- **Explicit Configuration**: Prefer explicit variable configuration over environment variables
- **Manual Path Specification**: Users manually specify paths rather than auto-detection

---

## 🔬 **Particle Analysis System**

### **Dual Classification Architecture**
- **Two Models**: Separate Weka models for porosity and shape classification
- **Independent Features**: Different feature sets for each model
- **Model 1**: Porosity classification (non-porous/porous)
- **Model 2**: Shape classification (round/satellites/splattered)
- **Dual Labels**: First model classifies porosity, second classifies shape

### **Feature Configuration**
- **Separate Feature Sets**: POROSITY_REQUIRED_FEATURES vs SHAPE_REQUIRED_FEATURES
- **Environment Variables**: Configurable via PARTICLE_ANALYSIS_POROSITY_FEATURES
- **Default Porosity**: "total_black_pixels"
- **Default Shape**: "Area;Circ.;Solidity;Round;Convexity;FeretRatio"
- **Case Insensitive**: Feature matching handles case variations

### **Data Processing**
- **Split Columns**: Separate Group columns into Porosity and Shape columns
- **Remove Redundant**: Remove redundant columns in Excel processing
- **Column Names**: 
  - Porosity: "Porous"/"NonPorous" 
  - Shape: "Round"/"Satellite"/"Splattered"

---

## 🎨 **Legend System**

### **Legend Requirements**
- **Visual Legend**: Show 6 dual classification colors with labels
- **Size**: Large legends, at least double default size (400×280 pixels)
- **Transparency**: Semi-transparent background for non-intrusive display
- **Movability**: Optionally movable as a whole unit

### **Legend Ordering** (CRITICAL)
**Exact order required:**
1. nonporous+round
2. nonporous+satellite  
3. nonporous+splattered
4. porous+round
5. porous+satellite
6. porous+splattered

### **Legend Implementation**
- **Primary Method**: Overlay-based with transparency
- **Fallback Method**: ROI Manager-based for compatibility
- **Font Sizes**: Title 24pt Bold, Labels 18pt Plain
- **Color Squares**: 24×24 pixels (primary) or 30×30 pixels (fallback)
- **Position**: Top-left corner (10,10)

---

## 🔧 **Package Management**

### **Always Use Package Managers**
- **JavaScript/Node.js**: npm install, yarn add, pnpm add
- **Python**: pip install, poetry add, conda install
- **Rust**: cargo add, cargo remove
- **Go**: go get, go mod tidy
- **Never**: Manually edit package.json, requirements.txt, etc.

### **Rationale**
- **Version Resolution**: Automatic dependency conflict resolution
- **Lock Files**: Maintain consistency across environments
- **Avoid Hallucination**: Prevent incorrect version numbers

---

## 🎯 **Development Workflow**

### **Planning Process**
1. **Information Gathering**: Use codebase-retrieval for context
2. **Detailed Planning**: Low-level, exhaustive planning before implementation
3. **File-by-file**: List each file that needs changes
4. **Conservative Edits**: Respect existing codebase structure

### **Testing Philosophy**
- **Write Tests**: Suggest unit tests for new code
- **Iterate**: Work diligently until tests pass
- **Better Outcomes**: Testing leads to improved implementations

### **Error Recovery**
- **Avoid Circles**: If going in circles, ask user for help
- **Conservative Approach**: More potentially damaging = more conservative
- **No Unauthorized Actions**: Never commit, push, merge, install, or deploy without permission

---

## 📁 **File Structure Preferences**

### **Configuration Files**
- **Environment Variables**: Use for paths and feature configurations
- **Hardcoded Defaults**: Provide sensible defaults in code
- **Cross-Platform**: Handle different OS path formats

### **Script Organization**
- **Single File**: Keep complete functionality in one file
- **Clear Sections**: Use comment headers for organization
- **Compact**: Remove debugging code for production versions

---

## 🔍 **Code Display**

### **Code Snippet Format**
- **Always Use**: `<augment_code_snippet>` XML tags
- **Attributes**: Include path= and mode="EXCERPT"
- **Backticks**: Use four backticks (````) instead of three
- **Brief**: Provide <10 lines, clickable for full context

### **Example Format**
```xml
<augment_code_snippet path="script.py" mode="EXCERPT">
````python
def process_image_dual(image_path, porosity_classifier, shape_classifier):
    """Process a single image with dual classification."""
    # Implementation details...
````
</augment_code_snippet>
```

---

## 🎯 **Key Principles Summary**

1. **Single Files**: Never split code across multiple files
2. **No f-strings**: Use .format() for Jython 2.7 compatibility  
3. **Unicode Direct**: Use Unicode strings directly, no conversion
4. **Minimal Logging**: Only essential output in production
5. **Package Managers**: Always use proper package management
6. **Conservative Edits**: Respect existing code structure
7. **Test-Driven**: Suggest testing for better outcomes
8. **Cross-Platform**: Ensure compatibility across OS
9. **Explicit Config**: Manual path specification preferred
10. **Large Legends**: Double-size legends with specific ordering

---

## 📝 **Usage Instructions**

### **For New Augment Installation**
1. **Share this document** in initial conversation
2. **Reference specific sections** when establishing preferences
3. **Mention key principles** during development discussions
4. **Use as checklist** for code review and implementation

### **For Ongoing Development**
- **Refer to sections** when questions arise about style/approach
- **Update document** if new preferences are established
- **Maintain consistency** with documented patterns

---

## 🔧 **Technical Implementation Details**

### **Environment Variables Configuration**
```bash
# Required Variables
PARTICLE_ANALYSIS_MACRO="/path/to/macro.ijm"
PARTICLE_ANALYSIS_POROSITY_MODEL="/path/to/porosity_model.model"
PARTICLE_ANALYSIS_SHAPE_MODEL="/path/to/shape_model.model"
PARTICLE_ANALYSIS_INPUT_DIR="/path/to/images"

# Optional Variables
PARTICLE_ANALYSIS_POROSITY_FEATURES="total_black_pixels"
PARTICLE_ANALYSIS_SHAPE_FEATURES="Area;Circ.;Solidity;Round;Convexity;FeretRatio"
PARTICLE_ANALYSIS_FILE_TYPES="tif"
PARTICLE_ANALYSIS_CLEANUP="true"
```

### **Color Scheme Mapping**
```python
DUAL_COLORS = {
    ("NonPorous", "round"): Color(0, 255, 0, 100),      # Green - Ideal
    ("NonPorous", "satellites"): Color(0, 200, 100, 100), # Green-cyan - Good
    ("NonPorous", "splattered"): Color(0, 150, 200, 100), # Green-blue - Acceptable
    ("Porous", "round"): Color(255, 100, 0, 100),        # Orange - Moderate
    ("Porous", "satellites"): Color(255, 0, 0, 100),     # Red - Poor
    ("Porous", "splattered"): Color(200, 0, 100, 100),   # Red-purple - Worst
}
```

### **CSV Export Structure**
- **Dual Combinations**: nonporous_round, nonporous_satellites, etc.
- **Porosity Totals**: porosity_NonPorous, porosity_Porous
- **Shape Totals**: shape_round, shape_satellites, shape_splattered
- **Summary Row**: TOTAL row with aggregated counts

### **Error Handling Patterns**
- **Feature Validation**: Case-insensitive feature matching
- **Index Bounds**: Check classification indices before array access
- **Model Compatibility**: Graceful degradation for model mismatches
- **Unicode Safety**: String concatenation instead of .format() for Unicode

### **Legend Technical Specs**
- **Overlay Method**: 400×280 pixels, semi-transparent white background
- **Fallback Method**: Individual ROI elements, 30×30 color squares
- **Font Specifications**: SansSerif family, Bold 24pt title, Plain 18pt labels
- **Position**: Fixed at (10,10) top-left corner

---

## 📚 **Project Files to Transfer**

### **Essential Files**
1. **Batch-processing-Compact.py** - Main dual classification script
2. **ImageJ Macro (.ijm)** - Particle detection and feature extraction
3. **Weka Models (.model)** - Trained porosity and shape classifiers
4. **This Documentation** - Complete preference reference

### **Configuration Examples**
- **Windows Paths**: Use forward slashes for cross-platform compatibility
- **Default Start Directory**: C:/Studium/Johann/Partikeluntersuchungen
- **File Extensions**: Default to "tif" but configurable

---

## 🎯 **Quick Setup Checklist**

### **New Augment Installation Setup**
- [ ] Share this documentation file in first conversation
- [ ] Mention "ImageJ/FIJI particle analysis workflow"
- [ ] Reference "dual classification system with separate models"
- [ ] Establish "no f-strings, use .format() for Jython 2.7"
- [ ] Confirm "single file preference, minimal logging"
- [ ] Set "Unicode direct usage, no conversion functions"
- [ ] Configure "legend ordering: nonporous→porous, round→satellites→splattered"

### **Development Environment**
- [ ] Transfer Batch-processing-Compact.py script
- [ ] Set up environment variables for models and paths
- [ ] Verify ImageJ/FIJI installation with Weka plugin
- [ ] Test script with sample images to confirm functionality
- [ ] Validate legend appearance and ordering

---

**End of Documentation**
*This document captures established preferences for ImageJ/FIJI particle analysis development with Augment AI Assistant.*

**Version**: 1.0
**Last Updated**: December 2024
**Compatibility**: ImageJ/FIJI with Jython 2.7, Weka plugin
