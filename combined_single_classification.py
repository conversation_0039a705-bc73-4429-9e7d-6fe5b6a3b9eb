import pandas as pd
import numpy as np
from sklearn.model_selection import cross_val_score, StratifiedKFold
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier, AdaBoostClassifier
from sklearn.tree import DecisionTreeClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.naive_bayes import GaussianNB
from sklearn.neighbors import KNeighborsClassifier
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
from sklearn.preprocessing import LabelEncoder, StandardScaler
from itertools import product
import warnings
warnings.filterwarnings('ignore')

class MultiLabelClassifierScanner:
    def __init__(self, X, y_porous, y_shape, test_size=0.2, cv_folds=5):
        """
        Initialize the scanner with your dataset
        
        Parameters:
        X: Feature matrix (particle measurements)
        y_porous: Binary labels for porous classification (0/1 or 'no'/'yes')
        y_shape: Multi-class labels for shape classification ('round'/'satellite'/'splattered')
        """
        self.X = X
        self.y_porous = y_porous
        self.y_shape = y_shape
        self.cv_folds = cv_folds
        self.scaler = StandardScaler()
        
        # Encode labels if they're strings
        self.porous_encoder = LabelEncoder()
        self.shape_encoder = LabelEncoder()
        
        self.y_porous_encoded = self.porous_encoder.fit_transform(y_porous)
        self.y_shape_encoded = self.shape_encoder.fit_transform(y_shape)
        
        # Scale features
        self.X_scaled = self.scaler.fit_transform(X)
        
        # Define classifier options
        self.binary_classifiers = {
            'LogisticRegression': LogisticRegression(random_state=42, max_iter=1000),
            'RandomForest': RandomForestClassifier(random_state=42, n_estimators=100),
            'SVM_rbf': SVC(random_state=42, kernel='rbf', probability=True),
            'SVM_linear': SVC(random_state=42, kernel='linear', probability=True),
            'DecisionTree': DecisionTreeClassifier(random_state=42),
            'GradientBoosting': GradientBoostingClassifier(random_state=42),
            'NaiveBayes': GaussianNB(),
            'KNN': KNeighborsClassifier(n_neighbors=5)
        }
        
        self.multiclass_classifiers = {
            'LogisticRegression': LogisticRegression(random_state=42, max_iter=1000, multi_class='ovr'),
            'RandomForest': RandomForestClassifier(random_state=42, n_estimators=100),
            'SVM_rbf': SVC(random_state=42, kernel='rbf', probability=True),
            'SVM_linear': SVC(random_state=42, kernel='linear', probability=True),
            'DecisionTree': DecisionTreeClassifier(random_state=42),
            'GradientBoosting': GradientBoostingClassifier(random_state=42),
            'NaiveBayes': GaussianNB(),
            'KNN': KNeighborsClassifier(n_neighbors=5)
        }
        
        self.results = []
    
    def evaluate_single_classifier(self, classifier, y_true, task_name):
        """Evaluate a single classifier using cross-validation"""
        try:
            cv_scores = cross_val_score(
                classifier, self.X_scaled, y_true,
                cv=StratifiedKFold(n_splits=self.cv_folds, shuffle=True, random_state=42),
                scoring='accuracy'
            )
            return cv_scores.mean(), cv_scores.std()
        except Exception as e:
            print(f"Error evaluating {task_name}: {e}")
            return 0.0, 0.0
    
    def evaluate_combination(self, porous_clf_name, shape_clf_name, verbose=False):
        """Evaluate a specific combination of classifiers"""
        
        # Get classifiers
        porous_clf = self.binary_classifiers[porous_clf_name]
        shape_clf = self.multiclass_classifiers[shape_clf_name]
        
        # Cross-validation setup
        cv = StratifiedKFold(n_splits=self.cv_folds, shuffle=True, random_state=42)
        
        porous_accuracies = []
        shape_accuracies = []
        combined_accuracies = []
        
        for train_idx, test_idx in cv.split(self.X_scaled, self.y_porous_encoded):
            X_train, X_test = self.X_scaled[train_idx], self.X_scaled[test_idx]
            y_porous_train, y_porous_test = self.y_porous_encoded[train_idx], self.y_porous_encoded[test_idx]
            y_shape_train, y_shape_test = self.y_shape_encoded[train_idx], self.y_shape_encoded[test_idx]
            
            # Train classifiers
            porous_clf.fit(X_train, y_porous_train)
            shape_clf.fit(X_train, y_shape_train)
            
            # Make predictions
            porous_pred = porous_clf.predict(X_test)
            shape_pred = shape_clf.predict(X_test)
            
            # Calculate individual accuracies
            porous_acc = accuracy_score(y_porous_test, porous_pred)
            shape_acc = accuracy_score(y_shape_test, shape_pred)
            
            # Calculate combined accuracy (both predictions must be correct)
            combined_correct = np.logical_and(
                porous_pred == y_porous_test,
                shape_pred == y_shape_test
            )
            combined_acc = np.mean(combined_correct)
            
            porous_accuracies.append(porous_acc)
            shape_accuracies.append(shape_acc)
            combined_accuracies.append(combined_acc)
        
        # Calculate means and standard deviations
        porous_mean, porous_std = np.mean(porous_accuracies), np.std(porous_accuracies)
        shape_mean, shape_std = np.mean(shape_accuracies), np.std(shape_accuracies)
        combined_mean, combined_std = np.mean(combined_accuracies), np.std(combined_accuracies)
        
        result = {
            'porous_classifier': porous_clf_name,
            'shape_classifier': shape_clf_name,
            'porous_accuracy': porous_mean,
            'porous_std': porous_std,
            'shape_accuracy': shape_mean,
            'shape_std': shape_std,
            'combined_accuracy': combined_mean,
            'combined_std': combined_std,
            'average_individual': (porous_mean + shape_mean) / 2
        }
        
        if verbose:
            print(f"{porous_clf_name} + {shape_clf_name}:")
            print(f"  Porous: {porous_mean:.4f} ± {porous_std:.4f}")
            print(f"  Shape: {shape_mean:.4f} ± {shape_std:.4f}")
            print(f"  Combined: {combined_mean:.4f} ± {combined_std:.4f}")
            print()
        
        return result
    
    def scan_all_combinations(self, verbose=True):
        """Scan all possible combinations of classifiers"""
        print("Scanning all classifier combinations...")
        print("=" * 50)
        
        total_combinations = len(self.binary_classifiers) * len(self.multiclass_classifiers)
        current = 0
        
        for porous_clf, shape_clf in product(self.binary_classifiers.keys(), self.multiclass_classifiers.keys()):
            current += 1
            if verbose:
                print(f"[{current}/{total_combinations}] Testing: {porous_clf} + {shape_clf}")
            
            result = self.evaluate_combination(porous_clf, shape_clf, verbose=False)
            self.results.append(result)
            
            if verbose:
                print(f"  Combined Accuracy: {result['combined_accuracy']:.4f} ± {result['combined_std']:.4f}")
        
        print("\nScanning complete!")
        return self.results
    
    def get_top_combinations(self, n=10, sort_by='combined_accuracy'):
        """Get top N combinations sorted by specified metric"""
        if not self.results:
            print("No results available. Run scan_all_combinations() first.")
            return None
        
        sorted_results = sorted(self.results, key=lambda x: x[sort_by], reverse=True)
        return sorted_results[:n]
    
    def print_results_summary(self, top_n=10):
        """Print a summary of the best results"""
        if not self.results:
            print("No results available. Run scan_all_combinations() first.")
            return
        
        print(f"\n{'='*80}")
        print(f"TOP {top_n} CLASSIFIER COMBINATIONS")
        print(f"{'='*80}")
        
        top_results = self.get_top_combinations(top_n)
        
        print(f"{'Rank':<4} {'Porous Classifier':<18} {'Shape Classifier':<18} {'Porous Acc':<12} {'Shape Acc':<12} {'Combined Acc':<15}")
        print("-" * 80)
        
        for i, result in enumerate(top_results, 1):
            print(f"{i:<4} {result['porous_classifier']:<18} {result['shape_classifier']:<18} "
                  f"{result['porous_accuracy']:.4f}±{result['porous_std']:.3f} "
                  f"{result['shape_accuracy']:.4f}±{result['shape_std']:.3f} "
                  f"{result['combined_accuracy']:.4f}±{result['combined_std']:.3f}")
    
    def export_results(self, filename='classifier_results.csv'):
        """Export all results to CSV"""
        if not self.results:
            print("No results available. Run scan_all_combinations() first.")
            return
        
        df = pd.DataFrame(self.results)
        df.to_csv(filename, index=False)
        print(f"Results exported to {filename}")

# Example usage:
def example_usage():
    """
    Example of how to use the scanner with your data
    Replace this with your actual data loading
    """
    
    # Generate example data (replace with your actual data loading)
    np.random.seed(42)
    n_samples = 1000
    n_features = 10
    
    # Example particle measurements
    X = np.random.randn(n_samples, n_features)
    
    # Example labels
    y_porous = np.random.choice(['no', 'yes'], n_samples)
    y_shape = np.random.choice(['round', 'satellite', 'splattered'], n_samples)
    
    # Initialize scanner
    scanner = MultiLabelClassifierScanner(X, y_porous, y_shape)
    
    # Run the scan
    results = scanner.scan_all_combinations(verbose=True)
    
    # Print summary
    scanner.print_results_summary(top_n=10)
    
    # Export results
    scanner.export_results('particle_classifier_results.csv')
    
    return scanner

# Uncomment the following line to run the example:
scanner = example_usage()

print("MultiLabel Classifier Scanner loaded!")
print("\nTo use with your data:")
print("1. Load your data into X (features), y_porous (binary), y_shape (3-class)")
print("2. scanner = MultiLabelClassifierScanner(X, y_porous, y_shape)")
print("3. scanner.scan_all_combinations()")
print("4. scanner.print_results_summary()")